#!/usr/bin/env python3
"""
10. Verification Suite - Mathematical verification and testing framework
Comprehensive testing and validation of all backtesting components
"""

import json
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass
import asyncio

from 1_base_backtester import Trade
from 2_ttm_squeeze_indicators import TTMSqueezeIndicators
from 3_trading_costs_calculator import TradingCostsCalculator, SimplifiedCosts
from 4_risk_management import RiskManager
from 5_performance_metrics import PerformanceAnalyzer

@dataclass
class VerificationResult:
    test_name: str
    passed: bool
    expected: Any
    actual: Any
    error_message: str = ""

class VerificationSuite:
    """
    Comprehensive verification and testing suite
    Validates mathematical accuracy and logical consistency
    """
    
    def __init__(self):
        self.results: List[VerificationResult] = []
        self.tolerance = 1e-6  # Floating point tolerance
    
    def add_result(self, test_name: str, passed: bool, expected: Any, actual: Any, error: str = ""):
        """Add verification result"""
        result = VerificationResult(test_name, passed, expected, actual, error)
        self.results.append(result)
        
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status}: {test_name}")
        if not passed:
            print(f"   Expected: {expected}")
            print(f"   Actual: {actual}")
            if error:
                print(f"   Error: {error}")
    
    def verify_trade_calculations(self):
        """Verify trade P&L calculations"""
        print("\n🔍 VERIFYING TRADE CALCULATIONS")
        print("-" * 50)
        
        # Test case 1: Simple profit calculation
        entry_price = 100.0
        exit_price = 110.0
        shares = 100
        
        expected_pnl = (exit_price - entry_price) * shares  # $1000
        expected_pct = (exit_price / entry_price - 1) * 100  # 10%
        
        actual_pnl = (exit_price - entry_price) * shares
        actual_pct = (exit_price / entry_price - 1) * 100
        
        self.add_result(
            "Trade P&L Calculation",
            abs(actual_pnl - expected_pnl) < self.tolerance,
            expected_pnl, actual_pnl
        )
        
        self.add_result(
            "Trade Percentage Calculation",
            abs(actual_pct - expected_pct) < self.tolerance,
            expected_pct, actual_pct
        )
        
        # Test case 2: Loss calculation
        exit_price_loss = 90.0
        expected_loss = (exit_price_loss - entry_price) * shares  # -$1000
        expected_loss_pct = (exit_price_loss / entry_price - 1) * 100  # -10%
        
        actual_loss = (exit_price_loss - entry_price) * shares
        actual_loss_pct = (exit_price_loss / entry_price - 1) * 100
        
        self.add_result(
            "Trade Loss Calculation",
            abs(actual_loss - expected_loss) < self.tolerance,
            expected_loss, actual_loss
        )
        
        self.add_result(
            "Trade Loss Percentage",
            abs(actual_loss_pct - expected_loss_pct) < self.tolerance,
            expected_loss_pct, actual_loss_pct
        )
    
    def verify_trading_costs(self):
        """Verify trading cost calculations"""
        print("\n💸 VERIFYING TRADING COSTS")
        print("-" * 50)
        
        # Test simplified costs
        simple_costs = SimplifiedCosts()
        
        shares = 100
        price = 150.0
        
        # Expected: $5 commission + 0.15% slippage
        expected_commission = 5.0
        expected_slippage = shares * price * 0.0015  # $22.50
        expected_total = expected_commission + expected_slippage  # $27.50
        
        actual_entry = simple_costs.calculate_entry_costs(shares, price)
        
        self.add_result(
            "Simplified Entry Costs",
            abs(actual_entry - expected_total) < self.tolerance,
            expected_total, actual_entry
        )
        
        # Test realistic costs
        realistic_costs = TradingCostsCalculator()
        
        cost_breakdown = realistic_costs.calculate_total_trading_costs(shares, price)
        
        # Verify components exist
        required_components = ['commission', 'sec_fees', 'slippage_cost', 'total_costs']
        for component in required_components:
            self.add_result(
                f"Cost Component: {component}",
                component in cost_breakdown,
                True, component in cost_breakdown
            )
        
        # Verify total is sum of components
        calculated_total = (cost_breakdown['commission'] + 
                          cost_breakdown['sec_fees'] + 
                          cost_breakdown['slippage_cost'])
        
        self.add_result(
            "Cost Components Sum",
            abs(cost_breakdown['total_costs'] - calculated_total) < self.tolerance,
            calculated_total, cost_breakdown['total_costs']
        )
    
    def verify_ttm_indicators(self):
        """Verify TTM-Squeeze indicator calculations"""
        print("\n📊 VERIFYING TTM-SQUEEZE INDICATORS")
        print("-" * 50)
        
        # Create test data
        np.random.seed(42)
        dates = pd.date_range('2024-01-01', periods=50, freq='D')
        prices = [100 + i + np.random.normal(0, 2) for i in range(50)]
        
        df = pd.DataFrame({
            'date': dates,
            'open': prices,
            'high': [p * 1.02 for p in prices],
            'low': [p * 0.98 for p in prices],
            'close': prices,
            'volume': [1000000] * 50
        })
        
        # Calculate indicators
        df = TTMSqueezeIndicators.calculate_ttm_indicators(df)
        
        # Verify required columns exist
        required_columns = [
            'ema5', 'ema8', 'ema21', 'bb_upper', 'bb_lower', 
            'atr14', 'kc_upper', 'kc_lower', 'squeeze', 'momentum', 'histogram'
        ]
        
        for col in required_columns:
            self.add_result(
                f"Indicator Column: {col}",
                col in df.columns,
                True, col in df.columns
            )
        
        # Verify EMA relationships (EMA5 should be more responsive than EMA21)
        if 'ema5' in df.columns and 'ema21' in df.columns:
            # In trending data, shorter EMA should have higher variance
            ema5_var = df['ema5'].var()
            ema21_var = df['ema21'].var()
            
            self.add_result(
                "EMA Responsiveness (EMA5 > EMA21 variance)",
                ema5_var > ema21_var,
                f"EMA5_var > EMA21_var", f"{ema5_var:.2f} vs {ema21_var:.2f}"
            )
        
        # Test signal detection
        for i in range(30, len(df)):
            has_signal, strength = TTMSqueezeIndicators.check_ttm_squeeze_signal(df, i, min_strength=6)
            
            # Signal strength should be between 0 and 8
            self.add_result(
                f"Signal Strength Range (idx {i})",
                0 <= strength <= 8,
                "0 <= strength <= 8", strength
            )
            
            if has_signal:
                print(f"   Found signal at index {i}: {strength}/8")
                break
    
    def verify_risk_management(self):
        """Verify risk management calculations"""
        print("\n⚖️  VERIFYING RISK MANAGEMENT")
        print("-" * 50)
        
        risk_mgr = RiskManager(starting_capital=30000)
        
        # Test position sizing
        shares = risk_mgr.calculate_position_size_basic(signal_strength=7, price=150.0)
        expected_allocation = 0.06  # 6% for 7/8 signals
        expected_value = 30000 * expected_allocation  # $1800
        expected_shares = int(expected_value / 150.0)  # 12 shares
        
        self.add_result(
            "Position Size Calculation",
            shares == expected_shares,
            expected_shares, shares
        )
        
        # Test position limits
        can_trade, reason = risk_mgr.check_position_limits("AAPL", 100, 150.0)
        
        self.add_result(
            "Position Limits Check",
            can_trade,  # Should pass with reasonable position
            True, can_trade
        )
        
        # Test daily loss limit
        can_trade_daily, daily_reason = risk_mgr.check_daily_loss_limit()
        
        self.add_result(
            "Daily Loss Limit Check",
            can_trade_daily,  # Should pass initially
            True, can_trade_daily
        )
    
    def verify_performance_metrics(self):
        """Verify performance metric calculations"""
        print("\n📈 VERIFYING PERFORMANCE METRICS")
        print("-" * 50)
        
        # Create sample trades
        sample_trades = [
            Trade("AAPL", "2024-01-01", 100.0, "2024-01-05", 110.0, 100, 7, 1000.0, 10.0, 4, "8EMA break"),
            Trade("MSFT", "2024-01-02", 200.0, "2024-01-08", 180.0, 50, 6, -1000.0, -10.0, 6, "8EMA break"),
            Trade("GOOGL", "2024-01-03", 150.0, "2024-01-10", 165.0, 100, 8, 1500.0, 10.0, 7, "Profit target"),
        ]
        
        analyzer = PerformanceAnalyzer(sample_trades, 30000, 31500)
        metrics = analyzer.calculate_basic_metrics()
        
        # Verify basic calculations
        expected_total_trades = 3
        expected_winning_trades = 2
        expected_losing_trades = 1
        expected_win_rate = 2/3 * 100  # 66.67%
        
        self.add_result(
            "Total Trades Count",
            metrics['total_trades'] == expected_total_trades,
            expected_total_trades, metrics['total_trades']
        )
        
        self.add_result(
            "Winning Trades Count",
            metrics['winning_trades'] == expected_winning_trades,
            expected_winning_trades, metrics['winning_trades']
        )
        
        self.add_result(
            "Win Rate Calculation",
            abs(metrics['win_rate'] - expected_win_rate) < self.tolerance,
            expected_win_rate, metrics['win_rate']
        )
        
        # Verify total return
        expected_total_return = (31500 / 30000 - 1) * 100  # 5%
        
        self.add_result(
            "Total Return Calculation",
            abs(metrics['total_return_pct'] - expected_total_return) < self.tolerance,
            expected_total_return, metrics['total_return_pct']
        )
    
    def verify_file_integrity(self):
        """Verify saved files can be loaded and contain expected data"""
        print("\n📁 VERIFYING FILE INTEGRITY")
        print("-" * 50)
        
        # Test files that should exist
        test_files = [
            'ttm_squeeze_results.json',
            'optimized_8ema_results.json',
            'realistic_backtest_results.json'
        ]
        
        for filename in test_files:
            try:
                with open(filename, 'r') as f:
                    data = json.load(f)
                
                # Check required keys
                required_keys = ['strategy', 'parameters', 'performance_report']
                has_all_keys = all(key in data for key in required_keys)
                
                self.add_result(
                    f"File Structure: {filename}",
                    has_all_keys,
                    f"Has keys: {required_keys}",
                    f"Missing: {[k for k in required_keys if k not in data]}"
                )
                
                # Check if trades data exists and is valid
                if 'trades' in data and data['trades']:
                    first_trade = data['trades'][0]
                    trade_keys = ['symbol', 'entry_price', 'exit_price', 'profit_loss']
                    has_trade_keys = all(key in first_trade for key in trade_keys)
                    
                    self.add_result(
                        f"Trade Data: {filename}",
                        has_trade_keys,
                        f"Has trade keys: {trade_keys}",
                        f"Missing: {[k for k in trade_keys if k not in first_trade]}"
                    )
                
            except FileNotFoundError:
                self.add_result(
                    f"File Exists: {filename}",
                    False,
                    "File exists", "File not found"
                )
            except json.JSONDecodeError as e:
                self.add_result(
                    f"JSON Valid: {filename}",
                    False,
                    "Valid JSON", f"JSON Error: {e}"
                )
    
    def run_comprehensive_verification(self):
        """Run all verification tests"""
        print("🔍 STARTING COMPREHENSIVE VERIFICATION SUITE")
        print("=" * 70)
        
        # Run all verification tests
        self.verify_trade_calculations()
        self.verify_trading_costs()
        self.verify_ttm_indicators()
        self.verify_risk_management()
        self.verify_performance_metrics()
        self.verify_file_integrity()
        
        # Summary
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r.passed)
        failed_tests = total_tests - passed_tests
        
        print("\n" + "=" * 70)
        print("📊 VERIFICATION SUMMARY")
        print("=" * 70)
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"Success Rate: {passed_tests/total_tests*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ FAILED TESTS:")
            for result in self.results:
                if not result.passed:
                    print(f"   • {result.test_name}")
                    if result.error_message:
                        print(f"     Error: {result.error_message}")
        else:
            print(f"\n🎉 ALL TESTS PASSED! System verification complete.")
        
        # Save verification report
        verification_report = {
            'verification_date': pd.Timestamp.now().isoformat(),
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'success_rate': passed_tests/total_tests*100,
            'test_results': [
                {
                    'test_name': r.test_name,
                    'passed': r.passed,
                    'expected': str(r.expected),
                    'actual': str(r.actual),
                    'error_message': r.error_message
                }
                for r in self.results
            ]
        }
        
        with open('verification_report.json', 'w') as f:
            json.dump(verification_report, f, indent=2)
        
        print(f"\n💾 Verification report saved to: verification_report.json")
        
        return passed_tests == total_tests

def main():
    """Run verification suite"""
    verifier = VerificationSuite()
    all_passed = verifier.run_comprehensive_verification()
    
    if all_passed:
        print("\n✅ SYSTEM READY FOR PRODUCTION USE")
    else:
        print("\n⚠️  SYSTEM REQUIRES ATTENTION - CHECK FAILED TESTS")
    
    return all_passed

if __name__ == "__main__":
    main()
