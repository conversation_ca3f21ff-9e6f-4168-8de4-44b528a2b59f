#!/usr/bin/env python3
"""
2. TTM-Squeeze Indicators - Exact implementation of 8-criteria TTM-Squeeze
All technical indicators and signal logic for TTM-Squeeze pattern
"""

import pandas as pd
import numpy as np
from typing import Tuple

class TTMSqueezeIndicators:
    """
    TTM-Squeeze Pattern Criteria Implementation
    8 Technical Criteria for momentum breakout detection
    """
    
    @staticmethod
    def calculate_linear_regression_slope(series: pd.Series, period: int, idx: int) -> float:
        """Calculate linear regression slope for momentum trend"""
        if idx < period:
            return 0
        
        y_values = series.iloc[idx-period+1:idx+1].values
        x_values = np.arange(len(y_values))
        
        if len(y_values) < 2:
            return 0
            
        # Calculate slope using least squares
        n = len(y_values)
        sum_x = np.sum(x_values)
        sum_y = np.sum(y_values)
        sum_xy = np.sum(x_values * y_values)
        sum_x2 = np.sum(x_values ** 2)
        
        denominator = n * sum_x2 - sum_x ** 2
        if denominator == 0:
            return 0
            
        slope = (n * sum_xy - sum_x * sum_y) / denominator
        return slope

    @staticmethod
    def calculate_ttm_indicators(df: pd.DataFrame) -> pd.DataFrame:
        """Calculate all TTM-Squeeze technical indicators"""
        
        # EMAs (including EMA5 as specified)
        df['ema5'] = df['close'].ewm(span=5).mean()
        df['ema8'] = df['close'].ewm(span=8).mean()
        df['ema21'] = df['close'].ewm(span=21).mean()
        df['ema50'] = df['close'].ewm(span=50).mean()
        
        # Bollinger Bands (20-period, 2 std dev)
        df['bb_middle'] = df['close'].rolling(20).mean()
        df['bb_std'] = df['close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (df['bb_std'] * 2)
        df['bb_lower'] = df['bb_middle'] - (df['bb_std'] * 2)
        
        # ATR (14-period)
        df['high_low'] = df['high'] - df['low']
        df['high_close'] = abs(df['high'] - df['close'].shift(1))
        df['low_close'] = abs(df['low'] - df['close'].shift(1))
        df['true_range'] = df[['high_low', 'high_close', 'low_close']].max(axis=1)
        df['atr14'] = df['true_range'].rolling(14).mean()
        
        # Keltner Channels (21-period EMA, 1.5 ATR)
        df['kc_middle'] = df['ema21']
        df['kc_upper'] = df['kc_middle'] + (df['atr14'] * 1.5)
        df['kc_lower'] = df['kc_middle'] - (df['atr14'] * 1.5)
        
        # TTM Squeeze (BB inside KC)
        df['squeeze'] = (df['bb_upper'] < df['kc_upper']) & (df['bb_lower'] > df['kc_lower'])
        
        # Momentum (Linear Regression of close vs 20-period SMA)
        df['sma20'] = df['close'].rolling(20).mean()
        df['momentum'] = df['close'] - df['sma20']
        
        # Histogram (momentum change)
        df['histogram'] = df['momentum'] - df['momentum'].shift(1)
        
        # Volume moving average
        if 'volume' in df.columns:
            df['volume_avg20'] = df['volume'].rolling(20).mean()
        else:
            df['volume_avg20'] = 1000000  # Default volume
            df['volume'] = 1000000
        
        # BB/ATR Ratio
        df['bb_width'] = df['bb_upper'] - df['bb_lower']
        df['bb_atr_ratio'] = df['bb_width'] / df['atr14']
        
        return df

    @classmethod
    def check_ttm_squeeze_signal(cls, df: pd.DataFrame, idx: int, min_strength: int = 7) -> Tuple[bool, int]:
        """
        EXACT TTM-Squeeze Pattern Implementation
        8 Criteria - Need 7/8 (87.5% threshold) by default
        
        Returns: (has_signal, signal_strength)
        """
        if idx < 25:  # Need enough data for all calculations
            return False, 0
            
        current = df.iloc[idx]
        prev = df.iloc[idx-1]
        prev2 = df.iloc[idx-2]
        prev3 = df.iloc[idx-3]
        
        score = 0
        criteria_met = []
        
        try:
            # 1. Histogram Turning MORE Positive
            # Current histogram > previous 3 bars AND positive
            hist_values = [current['histogram'], prev['histogram'], prev2['histogram'], prev3['histogram']]
            if (hist_values[0] > hist_values[1] and 
                hist_values[0] > hist_values[2] and 
                hist_values[0] > hist_values[3] and 
                hist_values[0] > 0):
                score += 1
                criteria_met.append("Histogram+")
            
            # 2. EMA Alignment & Trending
            # EMA8 > EMA21 AND EMA5, EMA8, EMA21 all trending up
            ema5_trending = current['ema5'] > prev['ema5']
            ema8_trending = current['ema8'] > prev['ema8']
            ema21_trending = current['ema21'] > prev['ema21']
            ema_aligned = current['ema8'] > current['ema21']
            
            if ema_aligned and ema5_trending and ema8_trending and ema21_trending:
                score += 1
                criteria_met.append("EMA_Align")
            
            # 3. TTM Squeeze (Optional - Always Passes)
            # Originally required BB inside KC, now optional
            score += 1
            criteria_met.append("Squeeze_OK")
            
            # 4. ATR Declining
            # 14-period ATR decreasing (volatility contracting)
            if current['atr14'] < prev['atr14']:
                score += 1
                criteria_met.append("ATR_Decline")
            
            # 5. BB/ATR Ratio < 1.5
            # Bollinger Band width relative to ATR under 1.5
            if pd.notna(current['bb_atr_ratio']) and current['bb_atr_ratio'] < 1.5:
                score += 1
                criteria_met.append("BB_ATR<1.5")
            
            # 6. Momentum Increasing
            # Linear regression momentum rising (20-period slope)
            momentum_slope = cls.calculate_linear_regression_slope(df['momentum'], 20, idx)
            if momentum_slope > 0:
                score += 1
                criteria_met.append("Mom_Rising")
            
            # 7. Volume Above Average
            # Current volume exceeding 20-period average
            if pd.notna(current['volume_avg20']) and current['volume'] > current['volume_avg20']:
                score += 1
                criteria_met.append("Vol_Above")
            
            # 8. Price Above 8EMA
            # Current close price above 8-period EMA
            if current['close'] > current['ema8']:
                score += 1
                criteria_met.append("Price>EMA8")
                
        except Exception as e:
            print(f"Error in TTM-Squeeze signal calculation: {e}")
            return False, 0
        
        # Debug output for strong signals
        if score >= 6:
            print(f"  Signal {score}/8: {', '.join(criteria_met)}")
        
        return score >= min_strength, score

    @staticmethod
    def check_squeeze_firing(df: pd.DataFrame, idx: int) -> bool:
        """Check if squeeze is firing (was in squeeze, now out)"""
        if idx < 1:
            return False
        
        current = df.iloc[idx]
        prev = df.iloc[idx-1]
        
        return prev['squeeze'] and not current['squeeze']

    @staticmethod
    def get_signal_quality_description(score: int) -> str:
        """Get description of signal quality based on score"""
        descriptions = {
            8: "Perfect Signal - All criteria met",
            7: "Strong Signal - 7/8 criteria met", 
            6: "Good Signal - 6/8 criteria met",
            5: "Weak Signal - 5/8 criteria met",
            4: "Poor Signal - 4/8 criteria met",
            3: "Very Poor Signal - 3/8 criteria met",
            2: "Minimal Signal - 2/8 criteria met",
            1: "Almost No Signal - 1/8 criteria met",
            0: "No Signal - 0/8 criteria met"
        }
        return descriptions.get(score, f"Unknown Signal - {score}/8 criteria met")

    @staticmethod
    def analyze_signal_breakdown(df: pd.DataFrame, idx: int) -> dict:
        """Detailed breakdown of each criterion for analysis"""
        if idx < 25:
            return {}
            
        current = df.iloc[idx]
        prev = df.iloc[idx-1]
        prev2 = df.iloc[idx-2]
        prev3 = df.iloc[idx-3]
        
        analysis = {}
        
        # 1. Histogram Analysis
        hist_values = [current['histogram'], prev['histogram'], prev2['histogram'], prev3['histogram']]
        analysis['histogram'] = {
            'current': hist_values[0],
            'improving': hist_values[0] > max(hist_values[1:]),
            'positive': hist_values[0] > 0,
            'passes': (hist_values[0] > hist_values[1] and 
                      hist_values[0] > hist_values[2] and 
                      hist_values[0] > hist_values[3] and 
                      hist_values[0] > 0)
        }
        
        # 2. EMA Analysis
        analysis['ema'] = {
            'ema8_above_ema21': current['ema8'] > current['ema21'],
            'ema5_trending_up': current['ema5'] > prev['ema5'],
            'ema8_trending_up': current['ema8'] > prev['ema8'],
            'ema21_trending_up': current['ema21'] > prev['ema21'],
            'passes': (current['ema8'] > current['ema21'] and
                      current['ema5'] > prev['ema5'] and
                      current['ema8'] > prev['ema8'] and
                      current['ema21'] > prev['ema21'])
        }
        
        # 3. ATR Analysis
        analysis['atr'] = {
            'current': current['atr14'],
            'previous': prev['atr14'],
            'declining': current['atr14'] < prev['atr14'],
            'passes': current['atr14'] < prev['atr14']
        }
        
        # 4. BB/ATR Ratio Analysis
        analysis['bb_atr'] = {
            'ratio': current['bb_atr_ratio'],
            'threshold': 1.5,
            'passes': pd.notna(current['bb_atr_ratio']) and current['bb_atr_ratio'] < 1.5
        }
        
        # 5. Momentum Analysis
        momentum_slope = TTMSqueezeIndicators.calculate_linear_regression_slope(df['momentum'], 20, idx)
        analysis['momentum'] = {
            'slope': momentum_slope,
            'increasing': momentum_slope > 0,
            'passes': momentum_slope > 0
        }
        
        # 6. Volume Analysis
        analysis['volume'] = {
            'current': current['volume'],
            'average': current['volume_avg20'],
            'above_average': current['volume'] > current['volume_avg20'],
            'passes': pd.notna(current['volume_avg20']) and current['volume'] > current['volume_avg20']
        }
        
        # 7. Price vs EMA8 Analysis
        analysis['price_ema8'] = {
            'price': current['close'],
            'ema8': current['ema8'],
            'above_ema8': current['close'] > current['ema8'],
            'passes': current['close'] > current['ema8']
        }
        
        return analysis

# Example usage and testing
if __name__ == "__main__":
    # Create sample data for testing
    dates = pd.date_range('2024-01-01', periods=100, freq='D')
    np.random.seed(42)
    
    # Generate realistic price data
    price = 100
    prices = []
    for _ in range(100):
        price += np.random.normal(0, 2)
        prices.append(max(price, 10))  # Ensure positive prices
    
    df = pd.DataFrame({
        'date': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.02))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.02))) for p in prices],
        'close': prices,
        'volume': [np.random.randint(100000, 1000000) for _ in range(100)]
    })
    
    # Calculate indicators
    df = TTMSqueezeIndicators.calculate_ttm_indicators(df)
    
    # Test signal detection
    for i in range(25, len(df)):
        has_signal, strength = TTMSqueezeIndicators.check_ttm_squeeze_signal(df, i)
        if has_signal:
            print(f"Signal found at index {i}: {strength}/8 strength")
            analysis = TTMSqueezeIndicators.analyze_signal_breakdown(df, i)
            print(f"Signal quality: {TTMSqueezeIndicators.get_signal_quality_description(strength)}")
            break
