#!/usr/bin/env python3
"""
6. TTM-Squeeze Strategy - Complete implementation with exact 8-criteria
Pure TTM-Squeeze strategy with compound reinvestment and 8EMA exits
"""

import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional

from 1_base_backtester import BaseBacktester, Trade
from 2_ttm_squeeze_indicators import TTMSqueezeIndicators
from 3_trading_costs_calculator import SimplifiedCosts
from 4_risk_management import RiskManager
from 5_performance_metrics import PerformanceAnalyzer

class TTMSqueezeStrategy(BaseBacktester):
    """
    Complete TTM-Squeeze Strategy Implementation
    - Exact 8-criteria TTM-Squeeze signals
    - 8EMA exits with profit protection
    - Compound reinvestment
    - Risk management
    """
    
    def __init__(self, starting_capital: float = 30000):
        super().__init__(starting_capital)
        
        # TTM-Squeeze specific parameters
        self.min_signal_strength = 7  # Need 7/8 criteria (87.5% threshold)
        self.max_position_pct = 0.08  # 8% max per position
        
        # Initialize components
        self.risk_manager = RiskManager(starting_capital)
        self.cost_calculator = SimplifiedCosts()
        
        # Enhanced stock universe for TTM-Squeeze
        self.stocks = [
            # High Beta Tech (good for momentum breakouts)
            'NVDA', 'AMD', 'TSLA', 'META', 'GOOGL', 'AAPL', 'MSFT', 'AMZN',
            # Growth Stocks with good volatility
            'AVGO', 'CRM', 'ORCL', 'ADBE', 'NFLX', 'INTU', 'NOW',
            # Semiconductor momentum plays
            'LRCX', 'AMAT', 'KLAC', 'ADI', 'TXN', 'QCOM',
            # Financial momentum
            'JPM', 'BAC', 'GS', 'AXP', 'SCHW',
            # Industrial/Energy breakouts
            'CAT', 'BA', 'RTX', 'XOM', 'CVX', 'COP',
            # Healthcare growth
            'UNH', 'JNJ', 'PFE', 'ABBV', 'TMO', 'DHR',
            # Consumer momentum
            'HD', 'LOW', 'NKE', 'DIS', 'MCD'
        ]
        
        # Track recent trades to avoid whipsaws
        self.recent_trades = {}
        self.min_trade_gap = 2  # Days between trades on same symbol

    def calculate_indicators(self, df):
        """Calculate TTM-Squeeze indicators"""
        return TTMSqueezeIndicators.calculate_ttm_indicators(df)

    def check_entry_signal(self, df, idx):
        """Check for TTM-Squeeze entry signal"""
        return TTMSqueezeIndicators.check_ttm_squeeze_signal(df, idx, self.min_signal_strength)

    def calculate_position_size(self, signal_strength: int, current_capital: float, price: float) -> int:
        """Position sizing with compound reinvestment"""
        
        # Base allocation by signal strength
        allocation_pct = {
            7: 0.06,   # 6% for 7/8 signals
            8: 0.08    # 8% for perfect 8/8 signals
        }
        
        base_allocation = allocation_pct.get(signal_strength, 0.04)
        
        # Calculate position value using current capital (compound effect)
        position_value = current_capital * base_allocation
        shares = int(position_value / price)
        
        return max(shares, 1) if shares > 0 else 0

    def check_exit_signal(self, df, entry_idx, current_idx):
        """Enhanced 8EMA exit with profit protection"""
        if current_idx <= entry_idx:
            return False, "No exit"
        
        current = df.iloc[current_idx]
        entry = df.iloc[entry_idx]
        
        # Primary Exit: 8EMA break
        if current['close'] < current['ema8']:
            # Additional confirmation for strong signals
            if current_idx > entry_idx + 1:
                prev = df.iloc[current_idx-1]
                if prev['close'] < prev['ema8']:  # Two consecutive closes below 8EMA
                    return True, "8EMA confirmed break"
            return True, "8EMA break"
        
        # Profit protection on large moves
        profit_pct = (current['close'] / entry['close'] - 1) * 100
        if profit_pct > 15:  # 15% profit protection
            return True, "Large profit protection"
        
        # Maximum hold period (prevent dead money)
        days_held = current_idx - entry_idx
        if days_held >= 20:
            return True, "Max hold period"
        
        return False, "Hold"

    async def backtest_symbol(self, symbol, start_date, end_date):
        """Backtest single symbol with TTM-Squeeze logic"""
        df = await self.fetch_historical_data(symbol, start_date, end_date)
        if df is None or len(df) < 60:
            return
        
        df = self.calculate_indicators(df)
        
        i = 30  # Start after indicators are calculated
        open_position = None
        
        while i < len(df):
            current_date = df.iloc[i]['date']
            
            # Check for exit if we have an open position
            if open_position:
                should_exit, exit_reason = self.check_exit_signal(df, open_position['entry_idx'], i)
                
                if should_exit:
                    # Close position with realistic costs
                    exit_price = df.iloc[i]['close']
                    days_held = i - open_position['entry_idx']
                    
                    # Calculate trading costs
                    exit_costs = self.cost_calculator.calculate_exit_costs(
                        open_position['shares'], exit_price
                    )
                    total_costs = open_position.get('entry_costs', 0) + exit_costs
                    
                    # Calculate P&L
                    gross_pnl = (exit_price - open_position['entry_price']) * open_position['shares']
                    net_pnl = gross_pnl - total_costs
                    profit_loss_pct = (exit_price / open_position['entry_price'] - 1) * 100
                    
                    trade = Trade(
                        symbol=symbol,
                        entry_date=open_position['entry_date'],
                        entry_price=open_position['entry_price'],
                        exit_date=current_date.strftime('%Y-%m-%d'),
                        exit_price=exit_price,
                        position_size=open_position['shares'],
                        signal_strength=open_position['signal_strength'],
                        profit_loss=net_pnl,
                        profit_loss_pct=profit_loss_pct,
                        days_held=days_held,
                        exit_reason=exit_reason,
                        trading_costs=total_costs
                    )
                    
                    self.trades.append(trade)
                    
                    # COMPOUND REINVESTMENT: Add/subtract P&L to capital
                    self.current_capital += net_pnl
                    
                    # Track recent trade to avoid immediate re-entry
                    self.recent_trades[symbol] = i
                    
                    print(f"TRADE: {symbol} {profit_loss_pct:+.1f}% ({exit_reason}) - Capital: ${self.current_capital:,.0f}")
                    
                    open_position = None
            
            # Check for new entry signal
            if not open_position and self.current_capital > 1000:
                # Avoid whipsaw trades
                if symbol in self.recent_trades:
                    if i - self.recent_trades[symbol] < self.min_trade_gap:
                        i += 1
                        continue
                
                has_signal, signal_strength = self.check_entry_signal(df, i)
                
                if has_signal:
                    entry_price = df.iloc[i]['close']
                    shares = self.calculate_position_size(signal_strength, self.current_capital, entry_price)
                    
                    if shares > 0:
                        # Calculate entry costs
                        entry_costs = self.cost_calculator.calculate_entry_costs(shares, entry_price)
                        position_cost = shares * entry_price + entry_costs
                        
                        if position_cost <= self.current_capital * 0.95:  # Keep 5% cash buffer
                            # Deduct entry costs from capital
                            self.current_capital -= entry_costs
                            
                            open_position = {
                                'entry_idx': i,
                                'entry_date': current_date.strftime('%Y-%m-%d'),
                                'entry_price': entry_price,
                                'shares': shares,
                                'signal_strength': signal_strength,
                                'entry_costs': entry_costs
                            }
                            
                            # Record signal
                            self.signals.append({
                                'symbol': symbol,
                                'date': current_date.strftime('%Y-%m-%d'),
                                'price': entry_price,
                                'signal_strength': signal_strength,
                                'capital_at_entry': self.current_capital
                            })
            
            i += 1

    async def run_backtest(self, months: int = 6):
        """Run TTM-Squeeze backtest with comprehensive reporting"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=months * 30)
        
        start_str = start_date.strftime('%Y-%m-%d')
        end_str = end_date.strftime('%Y-%m-%d')
        
        print(f"🎯 TTM-SQUEEZE STRATEGY BACKTEST")
        print(f"📅 Period: {start_str} to {end_str}")
        print(f"💰 Starting Capital: ${self.starting_capital:,}")
        print(f"📊 Testing {len(self.stocks)} stocks")
        print(f"🔍 Criteria: {self.min_signal_strength}/8 TTM-Squeeze signals required")
        print(f"🎯 Exit: 8EMA break with profit protection")
        print(f"💹 Auto-Reinvestment: Enabled")
        print(f"💸 Trading Costs: Included")
        print("=" * 70)
        
        # Process stocks in batches
        batch_size = 8
        for i in range(0, len(self.stocks), batch_size):
            batch = self.stocks[i:i + batch_size]
            print(f"Processing batch {i//batch_size + 1}: {batch[0]} to {batch[-1]}")
            
            tasks = [self.backtest_symbol(symbol, start_str, end_str) for symbol in batch]
            await asyncio.gather(*tasks, return_exceptions=True)
            
            await asyncio.sleep(1.0)  # Rate limiting
        
        return self.generate_results()

    def generate_results(self):
        """Generate comprehensive TTM-Squeeze results"""
        if not self.trades:
            print("❌ No trades executed!")
            return {}
        
        # Use performance analyzer for comprehensive metrics
        analyzer = PerformanceAnalyzer(self.trades, self.starting_capital, self.current_capital)
        
        # Print performance summary
        analyzer.print_performance_summary()
        
        # Generate comprehensive report
        report = analyzer.generate_comprehensive_report()
        
        # Add TTM-Squeeze specific analysis
        print(f"\n🎯 TTM-SQUEEZE SPECIFIC ANALYSIS:")
        
        signal_analysis = report['signal_strength_analysis']
        for signal_type, stats in signal_analysis.items():
            print(f"  {signal_type}: {stats['count']} trades, {stats['win_rate']:.1f}% win rate, {stats['avg_return']:+.2f}% avg return")
        
        exit_analysis = report['exit_reason_analysis']
        print(f"\n🚪 EXIT REASON ANALYSIS:")
        for reason, stats in exit_analysis.items():
            print(f"  {reason}: {stats['count']} trades ({stats['percentage']:.1f}%), {stats['win_rate']:.1f}% win rate")
        
        # Trading costs impact
        if 'trading_costs_analysis' in report and report['trading_costs_analysis']:
            costs = report['trading_costs_analysis']
            print(f"\n💸 TRADING COSTS IMPACT:")
            print(f"  Total Costs: ${costs['total_trading_costs']:.0f}")
            print(f"  Cost Impact: {costs['cost_impact_pct']:.2f}% of capital")
            print(f"  Gross Return: {costs['gross_return_pct']:+.2f}%")
            print(f"  Net Return: {costs['net_return_pct']:+.2f}%")
        
        # Compound growth analysis
        print(f"\n💹 COMPOUND GROWTH ANALYSIS:")
        print(f"📊 Capital Growth: ${self.starting_capital:,} → ${self.current_capital:,}")
        total_return_pct = (self.current_capital / self.starting_capital - 1) * 100
        print(f"📈 Total Return: {total_return_pct:+.2f}% over {len(self.trades)} trades")
        
        if len(self.trades) > 0:
            avg_return_per_trade = total_return_pct / len(self.trades)
            print(f"📊 Average Return per Trade: {avg_return_per_trade:+.3f}%")
        
        # Save results
        results = {
            'strategy': 'TTM-Squeeze',
            'parameters': {
                'min_signal_strength': self.min_signal_strength,
                'max_position_pct': self.max_position_pct,
                'starting_capital': self.starting_capital,
                'ending_capital': self.current_capital
            },
            'performance_report': report,
            'trades': [trade.__dict__ for trade in self.trades],
            'signals': self.signals
        }
        
        import json
        with open('ttm_squeeze_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Results saved to: ttm_squeeze_results.json")
        return results

async def main():
    """Run TTM-Squeeze strategy backtest"""
    async with TTMSqueezeStrategy(starting_capital=30000) as strategy:
        results = await strategy.run_backtest(months=6)
        return results

if __name__ == "__main__":
    asyncio.run(main())
