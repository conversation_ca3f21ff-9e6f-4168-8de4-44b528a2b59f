#!/usr/bin/env python3
"""
4. Risk Management - Position sizing, risk controls, and portfolio management
Comprehensive risk management for trading strategies
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta

@dataclass
class Position:
    symbol: str
    shares: int
    entry_price: float
    entry_date: str
    current_price: float
    unrealized_pnl: float
    unrealized_pnl_pct: float
    days_held: int
    signal_strength: int

class RiskManager:
    """
    Comprehensive risk management system
    Handles position sizing, portfolio limits, and risk controls
    """
    
    def __init__(self, starting_capital: float = 30000):
        self.starting_capital = starting_capital
        self.current_capital = starting_capital
        
        # Position Sizing Rules
        self.max_position_pct = 0.08           # Max 8% per position
        self.max_portfolio_risk = 0.20         # Max 20% portfolio at risk
        self.max_concurrent_positions = 8      # Max 8 positions
        
        # Daily Risk Limits
        self.max_daily_loss_pct = 0.03         # Max 3% daily loss
        self.max_daily_trades = 5              # Max 5 trades per day
        
        # Volatility-Based Sizing
        self.volatility_lookback = 20          # Days for volatility calculation
        self.volatility_target = 0.02          # Target 2% position volatility
        
        # Correlation Limits
        self.max_sector_concentration = 0.30   # Max 30% in one sector
        self.max_correlation_exposure = 0.50   # Max 50% in correlated positions
        
        # Stop Loss Rules
        self.max_position_loss_pct = 0.08      # Max 8% loss per position
        self.trailing_stop_pct = 0.05          # 5% trailing stop
        
        # Tracking
        self.daily_pnl = {}
        self.daily_trades = {}
        self.current_positions: Dict[str, Position] = {}
        self.sector_exposure = {}
        
    def calculate_position_size_basic(self, signal_strength: int, price: float) -> int:
        """Basic position sizing based on signal strength"""
        allocation_pct = {
            6: 0.04,   # 4% for 6/8 signals
            7: 0.06,   # 6% for 7/8 signals
            8: 0.08    # 8% for 8/8 signals (max allocation)
        }
        
        base_allocation = allocation_pct.get(signal_strength, 0.03)
        position_value = self.current_capital * base_allocation
        shares = int(position_value / price)
        
        return max(shares, 1) if shares > 0 else 0
    
    def calculate_position_size_volatility_adjusted(self, price: float, 
                                                   volatility: float,
                                                   signal_strength: int) -> int:
        """Volatility-adjusted position sizing"""
        if volatility <= 0:
            return self.calculate_position_size_basic(signal_strength, price)
        
        # Target position volatility
        target_vol = self.volatility_target
        
        # Adjust for signal strength
        strength_multiplier = {
            6: 0.8,   # Reduce size for weaker signals
            7: 1.0,   # Normal size
            8: 1.2    # Increase size for perfect signals
        }
        
        adjusted_target = target_vol * strength_multiplier.get(signal_strength, 1.0)
        
        # Calculate position size to achieve target volatility
        position_value = self.current_capital * (adjusted_target / volatility)
        
        # Apply maximum position limit
        max_position_value = self.current_capital * self.max_position_pct
        position_value = min(position_value, max_position_value)
        
        shares = int(position_value / price)
        return max(shares, 1) if shares > 0 else 0
    
    def check_position_limits(self, symbol: str, shares: int, price: float,
                            sector: str = None) -> Tuple[bool, str]:
        """Check if position passes all risk limits"""
        
        # Check maximum concurrent positions
        if len(self.current_positions) >= self.max_concurrent_positions:
            return False, f"Max concurrent positions ({self.max_concurrent_positions}) reached"
        
        # Check position size limit
        position_value = shares * price
        position_pct = position_value / self.current_capital
        if position_pct > self.max_position_pct:
            return False, f"Position size ({position_pct:.1%}) exceeds limit ({self.max_position_pct:.1%})"
        
        # Check sector concentration
        if sector:
            current_sector_exposure = self.sector_exposure.get(sector, 0)
            new_sector_exposure = (current_sector_exposure + position_value) / self.current_capital
            if new_sector_exposure > self.max_sector_concentration:
                return False, f"Sector exposure ({new_sector_exposure:.1%}) exceeds limit ({self.max_sector_concentration:.1%})"
        
        # Check daily trade limit
        today = datetime.now().strftime('%Y-%m-%d')
        daily_trades = self.daily_trades.get(today, 0)
        if daily_trades >= self.max_daily_trades:
            return False, f"Daily trade limit ({self.max_daily_trades}) reached"
        
        # Check available capital
        if position_value > self.current_capital * 0.95:  # Keep 5% cash buffer
            return False, "Insufficient capital (need 5% cash buffer)"
        
        return True, "Position approved"
    
    def check_daily_loss_limit(self) -> Tuple[bool, str]:
        """Check if daily loss limit has been reached"""
        today = datetime.now().strftime('%Y-%m-%d')
        daily_pnl = self.daily_pnl.get(today, 0)
        daily_loss_pct = abs(daily_pnl) / self.starting_capital
        
        if daily_pnl < 0 and daily_loss_pct > self.max_daily_loss_pct:
            return False, f"Daily loss limit ({self.max_daily_loss_pct:.1%}) exceeded"
        
        return True, "Daily loss within limits"
    
    def check_stop_loss(self, position: Position) -> Tuple[bool, str]:
        """Check if position should be stopped out"""
        
        # Maximum loss stop
        loss_pct = (position.current_price / position.entry_price - 1)
        if loss_pct <= -self.max_position_loss_pct:
            return True, f"Max loss stop ({self.max_position_loss_pct:.1%}) triggered"
        
        # Could add trailing stop logic here
        # trailing_stop_price = position.entry_price * (1 - self.trailing_stop_pct)
        # if position.current_price <= trailing_stop_price:
        #     return True, "Trailing stop triggered"
        
        return False, "No stop triggered"
    
    def update_position(self, symbol: str, current_price: float):
        """Update position with current price and P&L"""
        if symbol in self.current_positions:
            position = self.current_positions[symbol]
            position.current_price = current_price
            position.unrealized_pnl = (current_price - position.entry_price) * position.shares
            position.unrealized_pnl_pct = (current_price / position.entry_price - 1) * 100
            
            # Update days held (simplified)
            entry_date = datetime.strptime(position.entry_date, '%Y-%m-%d')
            position.days_held = (datetime.now() - entry_date).days
    
    def add_position(self, symbol: str, shares: int, entry_price: float,
                    entry_date: str, signal_strength: int, sector: str = None):
        """Add new position to portfolio"""
        position = Position(
            symbol=symbol,
            shares=shares,
            entry_price=entry_price,
            entry_date=entry_date,
            current_price=entry_price,
            unrealized_pnl=0.0,
            unrealized_pnl_pct=0.0,
            days_held=0,
            signal_strength=signal_strength
        )
        
        self.current_positions[symbol] = position
        
        # Update sector exposure
        if sector:
            position_value = shares * entry_price
            self.sector_exposure[sector] = self.sector_exposure.get(sector, 0) + position_value
        
        # Update daily trades
        today = datetime.now().strftime('%Y-%m-%d')
        self.daily_trades[today] = self.daily_trades.get(today, 0) + 1
    
    def remove_position(self, symbol: str, exit_price: float, sector: str = None) -> Optional[Position]:
        """Remove position from portfolio"""
        if symbol not in self.current_positions:
            return None
        
        position = self.current_positions[symbol]
        
        # Update sector exposure
        if sector:
            position_value = position.shares * position.entry_price
            self.sector_exposure[sector] = max(0, self.sector_exposure.get(sector, 0) - position_value)
        
        # Calculate final P&L
        final_pnl = (exit_price - position.entry_price) * position.shares
        
        # Update daily P&L
        today = datetime.now().strftime('%Y-%m-%d')
        self.daily_pnl[today] = self.daily_pnl.get(today, 0) + final_pnl
        
        # Update capital
        self.current_capital += final_pnl
        
        # Remove position
        del self.current_positions[symbol]
        
        return position
    
    def get_portfolio_summary(self) -> Dict:
        """Get current portfolio summary"""
        total_positions = len(self.current_positions)
        total_exposure = sum(pos.shares * pos.current_price for pos in self.current_positions.values())
        total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.current_positions.values())
        
        # Calculate portfolio metrics
        portfolio_pct_used = total_exposure / self.current_capital if self.current_capital > 0 else 0
        unrealized_return_pct = total_unrealized_pnl / self.starting_capital * 100
        
        # Get today's P&L
        today = datetime.now().strftime('%Y-%m-%d')
        today_pnl = self.daily_pnl.get(today, 0)
        today_trades = self.daily_trades.get(today, 0)
        
        return {
            'total_positions': total_positions,
            'total_exposure': total_exposure,
            'portfolio_pct_used': portfolio_pct_used,
            'total_unrealized_pnl': total_unrealized_pnl,
            'unrealized_return_pct': unrealized_return_pct,
            'current_capital': self.current_capital,
            'today_pnl': today_pnl,
            'today_trades': today_trades,
            'sector_exposure': dict(self.sector_exposure),
            'positions': {symbol: {
                'shares': pos.shares,
                'entry_price': pos.entry_price,
                'current_price': pos.current_price,
                'unrealized_pnl': pos.unrealized_pnl,
                'unrealized_pnl_pct': pos.unrealized_pnl_pct,
                'days_held': pos.days_held,
                'signal_strength': pos.signal_strength
            } for symbol, pos in self.current_positions.items()}
        }
    
    def get_risk_metrics(self) -> Dict:
        """Calculate portfolio risk metrics"""
        if not self.current_positions:
            return {}
        
        # Position concentration
        position_values = [pos.shares * pos.current_price for pos in self.current_positions.values()]
        total_exposure = sum(position_values)
        max_position_pct = max(position_values) / total_exposure if total_exposure > 0 else 0
        
        # Unrealized P&L distribution
        unrealized_pnls = [pos.unrealized_pnl_pct for pos in self.current_positions.values()]
        avg_unrealized_pnl = np.mean(unrealized_pnls)
        worst_position_pnl = min(unrealized_pnls)
        best_position_pnl = max(unrealized_pnls)
        
        # Days held distribution
        days_held = [pos.days_held for pos in self.current_positions.values()]
        avg_days_held = np.mean(days_held)
        
        return {
            'max_position_concentration': max_position_pct,
            'avg_unrealized_pnl_pct': avg_unrealized_pnl,
            'worst_position_pnl_pct': worst_position_pnl,
            'best_position_pnl_pct': best_position_pnl,
            'avg_days_held': avg_days_held,
            'portfolio_exposure_pct': total_exposure / self.current_capital * 100,
            'cash_available_pct': (self.current_capital - total_exposure) / self.current_capital * 100
        }

# Example usage
if __name__ == "__main__":
    # Test risk management system
    risk_mgr = RiskManager(starting_capital=30000)
    
    print("=== RISK MANAGEMENT SYSTEM TEST ===")
    
    # Test position sizing
    shares = risk_mgr.calculate_position_size_basic(signal_strength=7, price=150.0)
    print(f"Basic position size (7/8 signal, $150): {shares} shares")
    
    # Test volatility-adjusted sizing
    shares_vol = risk_mgr.calculate_position_size_volatility_adjusted(
        price=150.0, volatility=0.03, signal_strength=7
    )
    print(f"Volatility-adjusted size (3% vol): {shares_vol} shares")
    
    # Test position limits
    can_trade, reason = risk_mgr.check_position_limits("AAPL", shares, 150.0, "Technology")
    print(f"Position check: {can_trade} - {reason}")
    
    # Add some positions
    risk_mgr.add_position("AAPL", 100, 150.0, "2024-01-01", 7, "Technology")
    risk_mgr.add_position("MSFT", 80, 200.0, "2024-01-02", 8, "Technology")
    
    # Update prices
    risk_mgr.update_position("AAPL", 155.0)
    risk_mgr.update_position("MSFT", 195.0)
    
    # Get portfolio summary
    summary = risk_mgr.get_portfolio_summary()
    print(f"\nPortfolio Summary:")
    print(f"  Positions: {summary['total_positions']}")
    print(f"  Exposure: ${summary['total_exposure']:,.0f}")
    print(f"  Unrealized P&L: ${summary['total_unrealized_pnl']:,.0f}")
    print(f"  Portfolio Used: {summary['portfolio_pct_used']:.1%}")
    
    # Get risk metrics
    risk_metrics = risk_mgr.get_risk_metrics()
    print(f"\nRisk Metrics:")
    print(f"  Max Position: {risk_metrics['max_position_concentration']:.1%}")
    print(f"  Avg Unrealized: {risk_metrics['avg_unrealized_pnl_pct']:+.1f}%")
    print(f"  Cash Available: {risk_metrics['cash_available_pct']:.1f}%")
