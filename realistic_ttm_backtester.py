#!/usr/bin/env python3
"""
Realistic TTM-Squeeze Backtester with Enhanced Risk Management
Addresses critical gaps identified in the original backtester:
- Realistic trading costs and slippage
- Enhanced risk management
- Survivorship bias reduction
- Better performance metrics
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import List, Dict, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

@dataclass
class Trade:
    symbol: str
    entry_date: str
    entry_price: float
    exit_date: str
    exit_price: float
    position_size: int
    signal_strength: int
    profit_loss: float
    profit_loss_pct: float
    days_held: int
    exit_reason: str
    trading_costs: float = 0.0

class RealisticTTMBacktester:
    def __init__(self, starting_capital: float = 30000):
        self.fmp_key = 'K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7'
        self.session = None
        
        # Enhanced Risk Management
        self.max_position_size = 0.08  # Max 8% per position
        self.max_portfolio_risk = 0.25  # Max 25% total risk
        self.max_daily_loss = 0.03  # Max 3% daily loss
        self.max_concurrent_positions = 8  # Max 8 positions
        self.correlation_limit = 0.7  # Max correlation
        
        # Realistic Trading Costs
        self.commission_per_share = 0.005  # $0.005 per share
        self.min_commission = 1.0  # Min $1 commission
        self.sec_fee_rate = 0.0000221  # SEC fee
        self.slippage_base = 0.0015  # 0.15% base slippage
        
        # Capital and tracking
        self.starting_capital = starting_capital
        self.current_capital = starting_capital
        self.trades: List[Trade] = []
        self.signals = []
        self.daily_pnl = {}
        self.current_positions = {}
        
        # Diversified stock universe (reduces survivorship bias)
        self.stocks = [
            # Large Cap Tech
            'AAPL', 'MSFT', 'AMZN', 'NVDA', 'GOOGL', 'META', 'TSLA', 'ORCL', 'CRM', 'ADBE',
            # Large Cap Traditional
            'JNJ', 'JPM', 'V', 'PG', 'XOM', 'HD', 'CVX', 'MA', 'PFE', 'ABBV', 'KO', 'WMT',
            'BAC', 'UNH', 'VZ', 'T', 'DIS', 'NKE', 'MCD', 'GE', 'IBM', 'CAT', 'GS',
            # Growth/Mid Cap
            'AVGO', 'AMD', 'CSCO', 'TMO', 'COST', 'DHR', 'TXN', 'NFLX', 'QCOM', 'RTX',
            'SCHW', 'HON', 'LOW', 'AMGN', 'SPGI', 'INTU', 'AXP', 'BKNG',
            # Cyclical/Value
            'COP', 'UPS', 'BMY', 'PM', 'NEE', 'DE', 'MDT', 'BA', 'FCX', 'CSX',
            'MMC', 'CI', 'PYPL', 'TMUS', 'ZTS', 'CVS', 'MU', 'TGT', 'MO',
            # REITs/Utilities
            'AMT', 'EQIX', 'PLD', 'WM', 'APD', 'ICE', 'NSC', 'CME',
            # Healthcare/Biotech
            'ISRG', 'VRTX', 'GILD', 'SYK', 'ADP', 'REGN', 'BSX',
            # Tech Hardware
            'LRCX', 'AMAT', 'KLAC', 'ADI', 'INTC',
            # Financial
            'USB', 'PNC', 'CB', 'FIS', 'AON', 'PGR', 'ITW', 'CL',
            # Higher Risk (realistic market conditions)
            'SNAP', 'UBER', 'ROKU', 'ZM', 'DOCU', 'PLTR'
        ]

    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            connector=aiohttp.TCPConnector(limit=100)
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    def calculate_trading_costs(self, shares: int, price: float) -> float:
        """Calculate realistic trading costs"""
        commission = max(shares * self.commission_per_share, self.min_commission)
        sec_fee = shares * price * self.sec_fee_rate
        return commission + sec_fee

    def calculate_slippage(self, price: float, volume: float = None, 
                          market_cap: float = None, is_exit: bool = False) -> float:
        """Calculate realistic slippage"""
        slippage = self.slippage_base
        
        # Higher slippage for exits (market impact)
        if is_exit:
            slippage *= 1.2
            
        # Adjust for market cap
        if market_cap and market_cap < 1e9:  # Small cap
            slippage *= 2.5
        elif market_cap and market_cap < 10e9:  # Mid cap
            slippage *= 1.3
            
        # Adjust for volume
        if volume and volume < 1e6:  # Low volume
            slippage *= 1.8
            
        return price * (1 + slippage)

    def check_risk_limits(self, position_value: float, symbol: str = "") -> bool:
        """Enhanced risk management checks"""
        # Max position size
        if position_value > self.current_capital * self.max_position_size:
            return False
            
        # Max concurrent positions
        if len(self.current_positions) >= self.max_concurrent_positions:
            return False
            
        # Daily loss limit
        today = datetime.now().strftime('%Y-%m-%d')
        if today in self.daily_pnl:
            if self.daily_pnl[today] < -self.current_capital * self.max_daily_loss:
                return False
                
        return True

    def calculate_position_size(self, signal_strength: int, current_capital: float, 
                              price: float, volatility: float = None) -> int:
        """Enhanced position sizing with volatility adjustment"""
        
        # Base allocation by signal strength
        allocation_pct = {
            6: 0.025,  # 2.5% for 6/8 signals
            7: 0.04,   # 4% for 7/8 signals  
            8: 0.06    # 6% for 8/8 signals
        }
        
        base_allocation = allocation_pct.get(signal_strength, 0.02)
        
        # Volatility adjustment
        if volatility and volatility > 0:
            # Reduce position size for high volatility
            vol_adjustment = min(1.0, 0.15 / volatility)
            base_allocation *= vol_adjustment
            
        # Ensure within limits
        base_allocation = min(base_allocation, self.max_position_size)
        
        position_value = current_capital * base_allocation
        
        # Risk checks
        if not self.check_risk_limits(position_value):
            return 0
            
        shares = int(position_value / price)
        return max(shares, 0)

    def calculate_indicators(self, df):
        """Calculate technical indicators for TTM-Squeeze"""
        # EMAs
        df['ema8'] = df['close'].ewm(span=8).mean()
        df['ema21'] = df['close'].ewm(span=21).mean()
        
        # Bollinger Bands
        df['bb_middle'] = df['close'].rolling(20).mean()
        df['bb_std'] = df['close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (df['bb_std'] * 2)
        df['bb_lower'] = df['bb_middle'] - (df['bb_std'] * 2)
        
        # Keltner Channels
        df['atr14'] = df[['high', 'low', 'close']].apply(
            lambda x: max(x['high'] - x['low'], 
                         abs(x['high'] - x['close']), 
                         abs(x['low'] - x['close'])), axis=1
        ).rolling(14).mean()
        
        df['kc_middle'] = df['ema21']
        df['kc_upper'] = df['kc_middle'] + (df['atr14'] * 1.5)
        df['kc_lower'] = df['kc_middle'] - (df['atr14'] * 1.5)
        
        # TTM Squeeze
        df['squeeze'] = (df['bb_upper'] < df['kc_upper']) & (df['bb_lower'] > df['kc_lower'])
        
        # Momentum
        df['momentum'] = df['close'] - df['close'].rolling(20).mean()
        
        # Histogram (momentum change)
        df['histogram'] = df['momentum'] - df['momentum'].shift(1)
        
        return df

    async def fetch_historical_data(self, symbol, start_date, end_date):
        """Fetch historical data with error handling"""
        try:
            url = f"https://financialmodelingprep.com/api/v3/historical-price-full/{symbol}"
            params = {
                'apikey': self.fmp_key,
                'from': start_date,
                'to': end_date
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if 'historical' in data and len(data['historical']) > 50:
                        df = pd.DataFrame(data['historical'])
                        df['date'] = pd.to_datetime(df['date'])
                        return df.sort_values('date').reset_index(drop=True)
        except Exception as e:
            print(f"Error fetching {symbol}: {e}")
        return None

    def check_entry_signal(self, df, idx):
        """Enhanced TTM-Squeeze entry signal"""
        if idx < 4:
            return False, 0
            
        current = df.iloc[idx]
        prev = df.iloc[idx-1]
        prev2 = df.iloc[idx-2]
        prev3 = df.iloc[idx-3]
        
        score = 0
        
        try:
            # 1. Histogram improving (momentum acceleration)
            hist_values = [current['histogram'], prev['histogram'], 
                          prev2['histogram'], prev3['histogram']]
            if (hist_values[0] > hist_values[1] and 
                hist_values[0] > hist_values[2] and hist_values[0] > 0):
                score += 1
            
            # 2. EMA8 > EMA21 and trending up
            if (current['ema8'] > current['ema21'] and 
                current['ema8'] > prev['ema8']):
                score += 1
            
            # 3. Squeeze firing (was in squeeze, now out)
            if prev['squeeze'] and not current['squeeze']:
                score += 1
            
            # 4. ATR declining (volatility compression)
            if current['atr14'] < prev['atr14']:
                score += 1
            
            # 5. Price above EMA8
            if current['close'] > current['ema8']:
                score += 1
            
            # 6. Momentum positive
            if current['momentum'] > 0:
                score += 1
            
            # 7. Volume confirmation (if available)
            if 'volume' in current and 'volume' in prev:
                if current['volume'] > prev['volume'] * 1.2:
                    score += 1
            else:
                score += 1  # Auto-pass if no volume data
            
            # 8. Price breakout above recent high
            recent_high = df.iloc[max(0, idx-10):idx]['high'].max()
            if current['close'] > recent_high * 1.01:
                score += 1
                
        except Exception as e:
            print(f"Error in entry signal calculation: {e}")
            return False, 0
        
        return score >= 6, score

    def check_exit_signal(self, df, entry_idx, current_idx):
        """Enhanced exit signal logic"""
        if current_idx <= entry_idx:
            return False, "No exit"
        
        current = df.iloc[current_idx]
        entry = df.iloc[entry_idx]
        
        # 1. Stop loss: Price drops below EMA8
        if current['close'] < current['ema8']:
            return True, "EMA8 stop"
        
        # 2. Take profit: 2 ATR move
        atr_target = entry['close'] + (entry['atr14'] * 2)
        if current['close'] >= atr_target:
            return True, "2 ATR target"
        
        # 3. Maximum hold period (20 days)
        days_held = current_idx - entry_idx
        if days_held >= 20:
            return True, "Max hold period"
        
        # 4. Momentum reversal (3 consecutive negative days)
        if (current_idx >= entry_idx + 3 and 
            current['momentum'] < 0 and 
            df.iloc[current_idx-1]['momentum'] < 0 and 
            df.iloc[current_idx-2]['momentum'] < 0):
            return True, "Momentum reversal"
        
        # 5. Large gap down (>3%)
        if current_idx > entry_idx:
            prev_close = df.iloc[current_idx-1]['close']
            if (current['close'] - prev_close) / prev_close < -0.03:
                return True, "Gap down"
        
        return False, "Hold"

    async def run_backtest(self, months: int = 6):
        """Run enhanced backtest with realistic constraints"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=months * 30)
        
        start_str = start_date.strftime('%Y-%m-%d')
        end_str = end_date.strftime('%Y-%m-%d')
        
        print(f"🚀 REALISTIC TTM-SQUEEZE BACKTEST")
        print(f"📅 Period: {start_str} to {end_str}")
        print(f"💰 Starting Capital: ${self.starting_capital:,}")
        print(f"📊 Testing {len(self.stocks)} stocks")
        print(f"⚙️  Enhanced with: Trading Costs, Slippage, Risk Management")
        print("=" * 70)
        
        # Process stocks with rate limiting
        batch_size = 15
        for i in range(0, len(self.stocks), batch_size):
            batch = self.stocks[i:i + batch_size]
            print(f"Processing batch {i//batch_size + 1}: {batch[0]} to {batch[-1]}")
            
            tasks = [self.backtest_symbol(symbol, start_str, end_str) for symbol in batch]
            await asyncio.gather(*tasks, return_exceptions=True)
            
            await asyncio.sleep(1.5)  # Rate limiting
        
        return self.generate_results()

    async def backtest_symbol(self, symbol, start_date, end_date):
        """Backtest single symbol with enhanced logic"""
        df = await self.fetch_historical_data(symbol, start_date, end_date)
        if df is None or len(df) < 50:
            return

        df = self.calculate_indicators(df)

        i = 25  # Start after indicators are calculated
        open_position = None

        while i < len(df):
            current_date = df.iloc[i]['date']

            # Check for exit if we have an open position
            if open_position:
                should_exit, exit_reason = self.check_exit_signal(df, open_position['entry_idx'], i)

                if should_exit:
                    # Close position with realistic execution
                    exit_price = df.iloc[i]['close']
                    volume = df.iloc[i].get('volume', None)
                    exit_price_with_slippage = self.calculate_slippage(exit_price, volume, is_exit=True)

                    # Calculate all costs
                    exit_costs = self.calculate_trading_costs(open_position['shares'], exit_price_with_slippage)
                    total_costs = open_position.get('entry_costs', 0) + exit_costs

                    days_held = i - open_position['entry_idx']

                    # Net P&L after all costs
                    gross_pnl = (exit_price_with_slippage - open_position['entry_price']) * open_position['shares']
                    net_pnl = gross_pnl - total_costs
                    profit_loss_pct = (exit_price_with_slippage / open_position['entry_price'] - 1) * 100

                    trade = Trade(
                        symbol=symbol,
                        entry_date=open_position['entry_date'],
                        entry_price=open_position['entry_price'],
                        exit_date=current_date.strftime('%Y-%m-%d'),
                        exit_price=exit_price_with_slippage,
                        position_size=open_position['shares'],
                        signal_strength=open_position['signal_strength'],
                        profit_loss=net_pnl,
                        profit_loss_pct=profit_loss_pct,
                        days_held=days_held,
                        exit_reason=exit_reason,
                        trading_costs=total_costs
                    )

                    self.trades.append(trade)
                    self.current_capital += net_pnl

                    # Update daily P&L tracking
                    today = current_date.strftime('%Y-%m-%d')
                    if today not in self.daily_pnl:
                        self.daily_pnl[today] = 0
                    self.daily_pnl[today] += net_pnl

                    print(f"TRADE: {symbol} {profit_loss_pct:+.1f}% ({exit_reason}) - Capital: ${self.current_capital:,.0f}")

                    # Remove from positions
                    if symbol in self.current_positions:
                        del self.current_positions[symbol]

                    open_position = None

            # Check for new entry signal
            if not open_position and self.current_capital > 2000:
                has_signal, signal_strength = self.check_entry_signal(df, i)

                if has_signal:
                    entry_price = df.iloc[i]['close']
                    volatility = df.iloc[i]['atr14'] / entry_price if 'atr14' in df.columns else None

                    shares = self.calculate_position_size(signal_strength, self.current_capital,
                                                        entry_price, volatility)

                    if shares > 0:
                        # Apply slippage and costs
                        volume = df.iloc[i].get('volume', None)
                        entry_price_with_slippage = self.calculate_slippage(entry_price, volume)
                        entry_costs = self.calculate_trading_costs(shares, entry_price_with_slippage)

                        total_cost = shares * entry_price_with_slippage + entry_costs

                        if total_cost <= self.current_capital:
                            # Deduct costs
                            self.current_capital -= entry_costs

                            open_position = {
                                'entry_idx': i,
                                'entry_date': current_date.strftime('%Y-%m-%d'),
                                'entry_price': entry_price_with_slippage,
                                'shares': shares,
                                'signal_strength': signal_strength,
                                'entry_costs': entry_costs
                            }

                            self.current_positions[symbol] = open_position

                            # Record signal
                            self.signals.append({
                                'symbol': symbol,
                                'date': current_date.strftime('%Y-%m-%d'),
                                'price': entry_price_with_slippage,
                                'signal_strength': signal_strength
                            })

            i += 1

    def generate_results(self):
        """Generate comprehensive results with enhanced metrics"""
        if not self.trades:
            print("❌ No trades executed!")
            return {}

        # Calculate enhanced metrics
        metrics = self.calculate_enhanced_metrics()

        # Display results
        print("\n" + "=" * 70)
        print("🎯 REALISTIC BACKTEST RESULTS")
        print("=" * 70)

        print(f"💰 Starting Capital: ${metrics['starting_capital']:,}")
        print(f"💰 Ending Capital: ${metrics['ending_capital']:,}")
        print(f"📈 Total Return: {metrics['total_return_pct']:+.2f}%")
        print(f"💵 Net Profit/Loss: ${metrics['total_profit_loss']:+,.0f}")
        print(f"💸 Total Trading Costs: ${metrics['total_trading_costs']:,.0f}")
        print(f"🔢 Total Trades: {metrics['total_trades']}")
        print(f"✅ Win Rate: {metrics['win_rate']:.1f}%")
        print(f"❌ Loss Rate: {metrics['loss_rate']:.1f}%")
        print(f"📊 Average Win: {metrics['avg_win_pct']:+.2f}%")
        print(f"📊 Average Loss: {metrics['avg_loss_pct']:+.2f}%")
        print(f"⏱️  Average Hold: {metrics['avg_hold_days']:.1f} days")

        print(f"\n📊 RISK METRICS:")
        print(f"📉 Max Drawdown: {metrics['max_drawdown']:.2f}%")
        print(f"📊 Volatility: {metrics['volatility']:.2f}%")
        print(f"⚡ Sharpe Ratio: {metrics['sharpe_ratio']:.2f}")
        print(f"💪 Profit Factor: {metrics['profit_factor']:.2f}")
        print(f"🔥 Max Consecutive Wins: {metrics['max_consecutive_wins']}")
        print(f"❄️  Max Consecutive Losses: {metrics['max_consecutive_losses']}")

        print(f"\n🏆 BEST/WORST:")
        print(f"🥇 Best Trade: {metrics['best_trade']['symbol']} ({metrics['best_trade']['return_pct']:+.2f}%)")
        print(f"🥉 Worst Trade: {metrics['worst_trade']['symbol']} ({metrics['worst_trade']['return_pct']:+.2f}%)")

        print(f"\n🎯 SIGNAL STRENGTH ANALYSIS:")
        for signal_type, stats in metrics['signal_strength_analysis'].items():
            print(f"  {signal_type}: {stats['count']} trades, {stats['win_rate']:.1f}% win rate, {stats['avg_return']:+.2f}% avg return")

        # Save results
        results = {
            'metrics': metrics,
            'trades': [trade.__dict__ for trade in self.trades],
            'signals': self.signals
        }

        with open('realistic_ttm_backtest_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)

        print(f"\n💾 Results saved to: realistic_ttm_backtest_results.json")
        return results

    def calculate_enhanced_metrics(self):
        """Calculate comprehensive performance metrics"""
        total_trades = len(self.trades)
        winning_trades = [t for t in self.trades if t.profit_loss > 0]
        losing_trades = [t for t in self.trades if t.profit_loss < 0]

        win_rate = len(winning_trades) / total_trades * 100
        loss_rate = len(losing_trades) / total_trades * 100

        total_profit_loss = sum(t.profit_loss for t in self.trades)
        total_trading_costs = sum(t.trading_costs for t in self.trades)
        total_return_pct = (self.current_capital / self.starting_capital - 1) * 100

        # Average metrics
        avg_win = np.mean([t.profit_loss_pct for t in winning_trades]) if winning_trades else 0
        avg_loss = np.mean([t.profit_loss_pct for t in losing_trades]) if losing_trades else 0
        avg_hold_days = np.mean([t.days_held for t in self.trades])

        # Risk metrics
        returns = [t.profit_loss_pct for t in self.trades]
        volatility = np.std(returns) if len(returns) > 1 else 0

        # Sharpe ratio
        risk_free_rate = 2.0
        excess_return = np.mean(returns) - (risk_free_rate / 252)
        sharpe_ratio = excess_return / volatility if volatility > 0 else 0

        # Maximum drawdown
        cumulative_returns = np.cumsum([t.profit_loss for t in self.trades])
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdowns = (cumulative_returns - running_max) / self.starting_capital * 100
        max_drawdown = np.min(drawdowns) if len(drawdowns) > 0 else 0

        # Profit factor
        gross_profit = sum(t.profit_loss for t in winning_trades) if winning_trades else 0
        gross_loss = abs(sum(t.profit_loss for t in losing_trades)) if losing_trades else 1
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0

        # Consecutive wins/losses
        consecutive_wins = consecutive_losses = 0
        max_consecutive_wins = max_consecutive_losses = 0

        for trade in self.trades:
            if trade.profit_loss > 0:
                consecutive_wins += 1
                consecutive_losses = 0
                max_consecutive_wins = max(max_consecutive_wins, consecutive_wins)
            else:
                consecutive_losses += 1
                consecutive_wins = 0
                max_consecutive_losses = max(max_consecutive_losses, consecutive_losses)

        # Best and worst trades
        best_trade = max(self.trades, key=lambda t: t.profit_loss_pct)
        worst_trade = min(self.trades, key=lambda t: t.profit_loss_pct)

        # Signal strength analysis
        signal_stats = {}
        for strength in [6, 7, 8]:
            strength_trades = [t for t in self.trades if t.signal_strength == strength]
            if strength_trades:
                signal_stats[f'{strength}/8_signals'] = {
                    'count': len(strength_trades),
                    'win_rate': len([t for t in strength_trades if t.profit_loss > 0]) / len(strength_trades) * 100,
                    'avg_return': np.mean([t.profit_loss_pct for t in strength_trades])
                }

        return {
            'starting_capital': self.starting_capital,
            'ending_capital': self.current_capital,
            'total_return_pct': total_return_pct,
            'total_profit_loss': total_profit_loss,
            'total_trading_costs': total_trading_costs,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'loss_rate': loss_rate,
            'avg_win_pct': avg_win,
            'avg_loss_pct': avg_loss,
            'avg_hold_days': avg_hold_days,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'profit_factor': profit_factor,
            'max_consecutive_wins': max_consecutive_wins,
            'max_consecutive_losses': max_consecutive_losses,
            'best_trade': {
                'symbol': best_trade.symbol,
                'return_pct': best_trade.profit_loss_pct,
                'profit': best_trade.profit_loss
            },
            'worst_trade': {
                'symbol': worst_trade.symbol,
                'return_pct': worst_trade.profit_loss_pct,
                'loss': worst_trade.profit_loss
            },
            'signal_strength_analysis': signal_stats
        }

async def main():
    async with RealisticTTMBacktester(starting_capital=30000) as backtester:
        results = await backtester.run_backtest(months=6)
        return results

if __name__ == "__main__":
    asyncio.run(main())
