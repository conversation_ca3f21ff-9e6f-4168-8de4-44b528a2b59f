#!/usr/bin/env python3
"""
9. Strategy Comparison - Compare performance across different strategies
Comprehensive analysis and comparison framework
"""

import asyncio
import json
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Any

from 6_ttm_squeeze_strategy import TTMSqueezeStrategy
from 7_optimized_8ema_strategy import Optimized8EMAStrategy
from 8_realistic_backtester import RealisticBacktester

class StrategyComparison:
    """
    Comprehensive strategy comparison and analysis
    Compare multiple strategies across various metrics
    """
    
    def __init__(self, starting_capital: float = 30000):
        self.starting_capital = starting_capital
        self.strategies = {}
        self.results = {}
        
    async def run_all_strategies(self, months: int = 6):
        """Run all strategies and collect results"""
        
        print("🔄 RUNNING COMPREHENSIVE STRATEGY COMPARISON")
        print("=" * 70)
        
        # 1. TTM-Squeeze Strategy (Exact 7/8 criteria)
        print("\n1️⃣  Running TTM-Squeeze Strategy...")
        async with TTMSqueezeStrategy(self.starting_capital) as strategy:
            self.results['TTM_Squeeze'] = await strategy.run_backtest(months)
        
        # 2. Optimized 8EMA Strategy
        print("\n2️⃣  Running Optimized 8EMA Strategy...")
        async with Optimized8EMAStrategy(self.starting_capital) as strategy:
            self.results['Optimized_8EMA'] = await strategy.run_backtest(months)
        
        # 3. Realistic Backtester
        print("\n3️⃣  Running Realistic Backtester...")
        async with RealisticBacktester(self.starting_capital) as strategy:
            self.results['Realistic_Enhanced'] = await strategy.run_backtest(months)
        
        return self.results
    
    def compare_strategies(self) -> Dict[str, Any]:
        """Compare strategies across key metrics"""
        
        if not self.results:
            print("❌ No strategy results to compare!")
            return {}
        
        comparison = {
            'summary': {},
            'detailed_metrics': {},
            'rankings': {},
            'trade_analysis': {}
        }
        
        # Extract key metrics for each strategy
        for strategy_name, result in self.results.items():
            if not result or 'performance_report' not in result:
                continue
                
            report = result['performance_report']
            basic = report.get('basic_metrics', {})
            risk = report.get('risk_metrics', {})
            costs = report.get('trading_costs_analysis', {})
            
            comparison['summary'][strategy_name] = {
                'total_return_pct': basic.get('total_return_pct', 0),
                'total_trades': basic.get('total_trades', 0),
                'win_rate': basic.get('win_rate', 0),
                'avg_win_pct': basic.get('avg_win_pct', 0),
                'avg_loss_pct': basic.get('avg_loss_pct', 0),
                'avg_hold_days': basic.get('avg_hold_days', 0),
                'max_drawdown': risk.get('max_drawdown', 0),
                'sharpe_ratio': risk.get('sharpe_ratio', 0),
                'profit_factor': risk.get('profit_factor', 0),
                'total_costs': costs.get('total_trading_costs', 0),
                'cost_impact_pct': costs.get('cost_impact_pct', 0)
            }
        
        # Calculate rankings
        metrics_to_rank = [
            ('total_return_pct', 'desc'),
            ('win_rate', 'desc'),
            ('sharpe_ratio', 'desc'),
            ('profit_factor', 'desc'),
            ('max_drawdown', 'asc'),  # Lower is better
            ('total_trades', 'desc'),
            ('cost_impact_pct', 'asc')  # Lower is better
        ]
        
        for metric, direction in metrics_to_rank:
            values = [(name, data.get(metric, 0)) for name, data in comparison['summary'].items()]
            
            if direction == 'desc':
                ranked = sorted(values, key=lambda x: x[1], reverse=True)
            else:
                ranked = sorted(values, key=lambda x: x[1])
            
            comparison['rankings'][metric] = [name for name, _ in ranked]
        
        return comparison
    
    def print_comparison_report(self, comparison: Dict[str, Any]):
        """Print formatted comparison report"""
        
        print("\n" + "=" * 80)
        print("📊 COMPREHENSIVE STRATEGY COMPARISON REPORT")
        print("=" * 80)
        
        # Summary table
        print(f"\n📈 PERFORMANCE SUMMARY:")
        print("-" * 80)
        print(f"{'Strategy':<20} {'Return%':<10} {'Trades':<8} {'Win%':<8} {'Sharpe':<8} {'Max DD%':<10}")
        print("-" * 80)
        
        for strategy, metrics in comparison['summary'].items():
            print(f"{strategy:<20} "
                  f"{metrics['total_return_pct']:>8.2f}% "
                  f"{metrics['total_trades']:>6} "
                  f"{metrics['win_rate']:>6.1f}% "
                  f"{metrics['sharpe_ratio']:>6.2f} "
                  f"{metrics['max_drawdown']:>8.2f}%")
        
        # Risk-Reward Analysis
        print(f"\n⚖️  RISK-REWARD ANALYSIS:")
        print("-" * 80)
        print(f"{'Strategy':<20} {'Avg Win%':<10} {'Avg Loss%':<11} {'Ratio':<8} {'P.Factor':<10}")
        print("-" * 80)
        
        for strategy, metrics in comparison['summary'].items():
            avg_win = metrics['avg_win_pct']
            avg_loss = abs(metrics['avg_loss_pct'])
            ratio = avg_win / avg_loss if avg_loss > 0 else 0
            
            print(f"{strategy:<20} "
                  f"{avg_win:>8.2f}% "
                  f"{avg_loss:>9.2f}% "
                  f"{ratio:>6.2f} "
                  f"{metrics['profit_factor']:>8.2f}")
        
        # Trading Efficiency
        print(f"\n🔄 TRADING EFFICIENCY:")
        print("-" * 80)
        print(f"{'Strategy':<20} {'Hold Days':<10} {'Costs $':<10} {'Cost %':<8} {'Trades/Month':<12}")
        print("-" * 80)
        
        for strategy, metrics in comparison['summary'].items():
            trades_per_month = metrics['total_trades'] / 6  # 6 month backtest
            
            print(f"{strategy:<20} "
                  f"{metrics['avg_hold_days']:>8.1f} "
                  f"{metrics['total_costs']:>8.0f} "
                  f"{metrics['cost_impact_pct']:>6.2f}% "
                  f"{trades_per_month:>10.1f}")
        
        # Rankings
        print(f"\n🏆 STRATEGY RANKINGS:")
        print("-" * 50)
        
        key_rankings = [
            ('total_return_pct', 'Total Return'),
            ('win_rate', 'Win Rate'),
            ('sharpe_ratio', 'Sharpe Ratio'),
            ('max_drawdown', 'Max Drawdown (Lower=Better)')
        ]
        
        for metric, label in key_rankings:
            rankings = comparison['rankings'].get(metric, [])
            print(f"{label}:")
            for i, strategy in enumerate(rankings, 1):
                value = comparison['summary'][strategy].get(metric, 0)
                print(f"  {i}. {strategy}: {value:.2f}{'%' if 'pct' in metric or 'rate' in metric or 'drawdown' in metric else ''}")
            print()
        
        # Best Strategy Analysis
        print(f"\n🥇 BEST STRATEGY ANALYSIS:")
        print("-" * 50)
        
        # Find best overall strategy (weighted score)
        strategy_scores = {}
        for strategy in comparison['summary'].keys():
            score = 0
            
            # Return weight: 40%
            return_rank = comparison['rankings']['total_return_pct'].index(strategy)
            score += (len(comparison['rankings']['total_return_pct']) - return_rank) * 0.4
            
            # Sharpe ratio weight: 30%
            sharpe_rank = comparison['rankings']['sharpe_ratio'].index(strategy)
            score += (len(comparison['rankings']['sharpe_ratio']) - sharpe_rank) * 0.3
            
            # Win rate weight: 20%
            win_rank = comparison['rankings']['win_rate'].index(strategy)
            score += (len(comparison['rankings']['win_rate']) - win_rank) * 0.2
            
            # Max drawdown weight: 10% (inverted - lower is better)
            dd_rank = comparison['rankings']['max_drawdown'].index(strategy)
            score += dd_rank * 0.1
            
            strategy_scores[strategy] = score
        
        best_strategy = max(strategy_scores.keys(), key=lambda x: strategy_scores[x])
        best_metrics = comparison['summary'][best_strategy]
        
        print(f"🏆 Overall Winner: {best_strategy}")
        print(f"   Return: {best_metrics['total_return_pct']:+.2f}%")
        print(f"   Win Rate: {best_metrics['win_rate']:.1f}%")
        print(f"   Sharpe Ratio: {best_metrics['sharpe_ratio']:.2f}")
        print(f"   Max Drawdown: {best_metrics['max_drawdown']:.2f}%")
        print(f"   Total Trades: {best_metrics['total_trades']}")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        print("-" * 50)
        
        if best_strategy == 'TTM_Squeeze':
            print("• TTM-Squeeze shows high selectivity but may need parameter tuning")
            print("• Consider lowering signal threshold to 6/8 for more opportunities")
            print("• Focus on trending market conditions for better performance")
        elif best_strategy == 'Optimized_8EMA':
            print("• Optimized 8EMA provides good balance of returns and trade frequency")
            print("• Consider adding volatility filters for risk management")
            print("• Monitor for overtrading in choppy markets")
        elif best_strategy == 'Realistic_Enhanced':
            print("• Realistic approach provides conservative but steady returns")
            print("• Trading costs significantly impact performance")
            print("• Consider optimizing position sizing and entry timing")
        
        print("• All strategies benefit from compound reinvestment")
        print("• Consider market regime analysis for strategy selection")
        print("• Regular rebalancing and parameter optimization recommended")
    
    def save_comparison_report(self, comparison: Dict[str, Any]):
        """Save detailed comparison report"""
        
        # Prepare comprehensive report
        report = {
            'comparison_date': datetime.now().isoformat(),
            'starting_capital': self.starting_capital,
            'backtest_period_months': 6,
            'strategies_compared': list(self.results.keys()),
            'comparison_metrics': comparison,
            'detailed_results': self.results
        }
        
        # Save to JSON
        with open('strategy_comparison_report.json', 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        # Create CSV summary for easy analysis
        summary_data = []
        for strategy, metrics in comparison['summary'].items():
            row = {'Strategy': strategy}
            row.update(metrics)
            summary_data.append(row)
        
        df = pd.DataFrame(summary_data)
        df.to_csv('strategy_comparison_summary.csv', index=False)
        
        print(f"\n💾 Comparison report saved:")
        print(f"   📄 JSON: strategy_comparison_report.json")
        print(f"   📊 CSV: strategy_comparison_summary.csv")

async def main():
    """Run comprehensive strategy comparison"""
    
    print("🚀 STARTING COMPREHENSIVE STRATEGY COMPARISON")
    print(f"💰 Starting Capital: $30,000")
    print(f"📅 Backtest Period: 6 months")
    print(f"🔍 Strategies: TTM-Squeeze, Optimized 8EMA, Realistic Enhanced")
    
    # Initialize comparison
    comparison_engine = StrategyComparison(starting_capital=30000)
    
    # Run all strategies
    results = await comparison_engine.run_all_strategies(months=6)
    
    # Compare strategies
    comparison = comparison_engine.compare_strategies()
    
    # Print comparison report
    comparison_engine.print_comparison_report(comparison)
    
    # Save detailed report
    comparison_engine.save_comparison_report(comparison)
    
    return comparison

if __name__ == "__main__":
    asyncio.run(main())
