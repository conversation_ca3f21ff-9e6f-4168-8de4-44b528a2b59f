#!/usr/bin/env python3
"""
8. Realistic Backtester - Enhanced backtester with realistic trading conditions
Includes comprehensive trading costs, slippage, and risk management
"""

import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional

from 1_base_backtester import BaseBacktester, Trade
from 3_trading_costs_calculator import TradingCostsCalculator
from 4_risk_management import RiskManager
from 5_performance_metrics import PerformanceAnalyzer

class RealisticBacktester(BaseBacktester):
    """
    Realistic Backtesting Environment
    - Comprehensive trading costs (commission, slippage, SEC fees)
    - Market impact modeling
    - Enhanced risk management
    - Realistic execution assumptions
    """
    
    def __init__(self, starting_capital: float = 30000):
        super().__init__(starting_capital)
        
        # Initialize realistic components
        self.cost_calculator = TradingCostsCalculator()
        self.risk_manager = RiskManager(starting_capital)
        
        # Enhanced risk parameters
        self.max_position_size = 0.08      # Max 8% per position
        self.max_concurrent_positions = 8   # Max 8 positions
        self.max_daily_loss = 0.03         # Max 3% daily loss
        self.min_signal_strength = 6       # Minimum signal strength
        
        # Diversified stock universe (reduced survivorship bias)
        self.stocks = [
            # Large Cap Tech
            'AAPL', 'MSFT', 'AMZN', 'NVDA', 'GOOGL', 'TSLA', 'META',
            # Traditional Blue Chips
            'JNJ', 'JPM', 'V', 'PG', 'UNH', 'HD', 'DIS',
            # Growth Stocks
            'CRM', 'ADBE', 'NFLX', 'ORCL', 'INTU', 'AVGO',
            # Financial Services
            'BAC', 'GS', 'AXP', 'SCHW', 'MA',
            # Industrial/Energy
            'CAT', 'BA', 'RTX', 'XOM', 'CVX', 'COP',
            # Healthcare/Pharma
            'PFE', 'ABBV', 'TMO', 'DHR', 'BMY', 'AMGN',
            # Consumer Goods
            'KO', 'PEP', 'WMT', 'NKE', 'MCD', 'LOW',
            # Technology/Semiconductors
            'AMD', 'QCOM', 'TXN', 'ADI', 'LRCX', 'AMAT',
            # Telecommunications
            'VZ', 'T', 'TMUS', 'CMCSA',
            # Utilities/REITs
            'NEE', 'SO', 'DUK',
            # Higher Risk (realistic conditions)
            'SNAP', 'UBER', 'ROKU', 'PLTR', 'SPCE'
        ]
        
        # Track positions and daily P&L
        self.daily_pnl = {}
        self.current_positions = {}

    def calculate_indicators(self, df):
        """Calculate comprehensive technical indicators"""
        # EMAs
        df['ema8'] = df['close'].ewm(span=8).mean()
        df['ema21'] = df['close'].ewm(span=21).mean()
        df['ema50'] = df['close'].ewm(span=50).mean()
        
        # Bollinger Bands
        df['bb_middle'] = df['close'].rolling(20).mean()
        df['bb_std'] = df['close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (df['bb_std'] * 2)
        df['bb_lower'] = df['bb_middle'] - (df['bb_std'] * 2)
        
        # ATR and volatility
        df['high_low'] = df['high'] - df['low']
        df['high_close'] = abs(df['high'] - df['close'].shift(1))
        df['low_close'] = abs(df['low'] - df['close'].shift(1))
        df['true_range'] = df[['high_low', 'high_close', 'low_close']].max(axis=1)
        df['atr14'] = df['true_range'].rolling(14).mean()
        
        # Keltner Channels
        df['kc_middle'] = df['ema21']
        df['kc_upper'] = df['kc_middle'] + (df['atr14'] * 1.5)
        df['kc_lower'] = df['kc_middle'] - (df['atr14'] * 1.5)
        
        # TTM Squeeze
        df['squeeze'] = (df['bb_upper'] < df['kc_upper']) & (df['bb_lower'] > df['kc_lower'])
        
        # Momentum indicators
        df['sma20'] = df['close'].rolling(20).mean()
        df['momentum'] = df['close'] - df['sma20']
        df['histogram'] = df['momentum'] - df['momentum'].shift(1)
        
        # Volume analysis
        if 'volume' in df.columns:
            df['volume_avg20'] = df['volume'].rolling(20).mean()
            df['volume_ratio'] = df['volume'] / df['volume_avg20']
        
        return df

    def check_realistic_entry_signal(self, df, idx):
        """Realistic entry signal with comprehensive checks"""
        if idx < 25:
            return False, 0
            
        current = df.iloc[idx]
        prev = df.iloc[idx-1]
        
        score = 0
        
        try:
            # 1. Trend alignment
            if current['ema8'] > current['ema21'] and current['ema21'] > current['ema50']:
                score += 2
            elif current['ema8'] > current['ema21']:
                score += 1
            
            # 2. Price momentum
            if current['close'] > current['ema8'] and current['momentum'] > 0:
                score += 1
            
            # 3. Histogram improvement
            if current['histogram'] > prev['histogram'] and current['histogram'] > 0:
                score += 1
            
            # 4. Squeeze conditions
            if prev['squeeze'] and not current['squeeze']:  # Squeeze firing
                score += 2
            elif not current['squeeze']:  # Not in squeeze
                score += 1
            
            # 5. Volume confirmation
            if 'volume_ratio' in current and current['volume_ratio'] > 1.1:
                score += 1
            
            # 6. ATR filter (avoid dead markets)
            atr_avg = df['atr14'].rolling(20).mean().iloc[idx]
            if current['atr14'] > atr_avg * 0.8:
                score += 1
            
            # 7. Breakout confirmation
            recent_high = df.iloc[max(0, idx-10):idx]['high'].max()
            if current['close'] > recent_high * 1.002:  # 0.2% breakout
                score += 1
                
        except Exception as e:
            print(f"Error in realistic entry signal: {e}")
            return False, 0
        
        return score >= self.min_signal_strength, score

    def check_realistic_exit_signal(self, df, entry_idx, current_idx):
        """Realistic exit signal with multiple conditions"""
        if current_idx <= entry_idx:
            return False, "No exit"
        
        current = df.iloc[current_idx]
        entry = df.iloc[entry_idx]
        prev = df.iloc[current_idx-1]
        
        # 1. 8EMA stop (primary exit)
        if current['close'] < current['ema8']:
            return True, "EMA8 stop"
        
        # 2. 2 ATR profit target
        atr_target = entry['close'] + (entry['atr14'] * 2)
        if current['close'] >= atr_target:
            return True, "2 ATR target"
        
        # 3. Momentum reversal
        if (current['histogram'] < prev['histogram'] and 
            current['histogram'] < 0 and 
            current['momentum'] < 0):
            return True, "Momentum reversal"
        
        # 4. Gap down (risk management)
        gap_down = (current['open'] / prev['close'] - 1) * 100
        if gap_down < -3:  # 3% gap down
            return True, "Gap down"
        
        # 5. Maximum hold period
        days_held = current_idx - entry_idx
        if days_held >= 15:
            return True, "Max hold period"
        
        # 6. Volatility spike (risk management)
        if current['atr14'] > entry['atr14'] * 2:
            return True, "Volatility spike"
        
        return False, "Hold"

    def calculate_realistic_position_size(self, signal_strength: int, price: float, 
                                        volatility: Optional[float] = None) -> int:
        """Realistic position sizing with volatility adjustment"""
        
        # Base allocation by signal strength
        base_allocations = {
            6: 0.03,   # 3% for weak signals
            7: 0.05,   # 5% for good signals
            8: 0.08    # 8% for strong signals
        }
        
        base_allocation = base_allocations.get(signal_strength, 0.02)
        
        # Adjust for volatility if available
        if volatility and volatility > 0:
            # Reduce size for high volatility stocks
            if volatility > 0.03:  # >3% daily volatility
                base_allocation *= 0.7
            elif volatility > 0.02:  # >2% daily volatility
                base_allocation *= 0.85
        
        # Calculate position size
        position_value = self.current_capital * base_allocation
        shares = int(position_value / price)
        
        # Apply maximum position size limit
        max_position_value = self.current_capital * self.max_position_size
        if position_value > max_position_value:
            shares = int(max_position_value / price)
        
        return max(shares, 1) if shares > 0 else 0

    async def backtest_symbol(self, symbol, start_date, end_date):
        """Backtest single symbol with realistic conditions"""
        df = await self.fetch_historical_data(symbol, start_date, end_date)
        if df is None or len(df) < 50:
            return
        
        df = self.calculate_indicators(df)
        
        i = 25  # Start after indicators are calculated
        open_position = None
        
        while i < len(df):
            current_date = df.iloc[i]['date']
            
            # Check daily loss limit
            can_trade, reason = self.risk_manager.check_daily_loss_limit()
            if not can_trade:
                i += 1
                continue
            
            # Check for exit if we have an open position
            if open_position:
                should_exit, exit_reason = self.check_realistic_exit_signal(
                    df, open_position['entry_idx'], i
                )
                
                if should_exit:
                    # Calculate realistic exit costs
                    exit_price = df.iloc[i]['close']
                    volume = df.iloc[i].get('volume', None)
                    volatility = df.iloc[i]['atr14'] / exit_price if 'atr14' in df.columns else None
                    
                    exit_costs = self.cost_calculator.calculate_total_trading_costs(
                        open_position['shares'], exit_price, volume, volatility, is_exit=True
                    )
                    
                    total_costs = open_position.get('entry_costs', 0) + exit_costs['total_costs']
                    
                    days_held = i - open_position['entry_idx']
                    
                    # Net P&L after all costs
                    gross_pnl = (exit_costs['adjusted_price'] - open_position['entry_price']) * open_position['shares']
                    net_pnl = gross_pnl - total_costs
                    profit_loss_pct = (exit_costs['adjusted_price'] / open_position['entry_price'] - 1) * 100
                    
                    trade = Trade(
                        symbol=symbol,
                        entry_date=open_position['entry_date'],
                        entry_price=open_position['entry_price'],
                        exit_date=current_date.strftime('%Y-%m-%d'),
                        exit_price=exit_costs['adjusted_price'],
                        position_size=open_position['shares'],
                        signal_strength=open_position['signal_strength'],
                        profit_loss=net_pnl,
                        profit_loss_pct=profit_loss_pct,
                        days_held=days_held,
                        exit_reason=exit_reason,
                        trading_costs=total_costs
                    )
                    
                    self.trades.append(trade)
                    self.current_capital += net_pnl
                    
                    # Update daily P&L tracking
                    today = current_date.strftime('%Y-%m-%d')
                    if today not in self.daily_pnl:
                        self.daily_pnl[today] = 0
                    self.daily_pnl[today] += net_pnl
                    
                    print(f"TRADE: {symbol} {profit_loss_pct:+.1f}% ({exit_reason}) - Capital: ${self.current_capital:,.0f}")
                    
                    # Remove from positions
                    if symbol in self.current_positions:
                        del self.current_positions[symbol]
                    
                    open_position = None
            
            # Check for new entry signal
            if (not open_position and 
                self.current_capital > 2000 and 
                len(self.current_positions) < self.max_concurrent_positions):
                
                has_signal, signal_strength = self.check_realistic_entry_signal(df, i)
                
                if has_signal:
                    entry_price = df.iloc[i]['close']
                    volatility = df.iloc[i]['atr14'] / entry_price if 'atr14' in df.columns else None
                    
                    shares = self.calculate_realistic_position_size(
                        signal_strength, entry_price, volatility
                    )
                    
                    if shares > 0:
                        # Calculate realistic entry costs
                        volume = df.iloc[i].get('volume', None)
                        
                        entry_costs = self.cost_calculator.calculate_total_trading_costs(
                            shares, entry_price, volume, volatility, is_exit=False
                        )
                        
                        total_cost = shares * entry_costs['adjusted_price'] + entry_costs['total_costs']
                        
                        if total_cost <= self.current_capital:
                            # Deduct costs
                            self.current_capital -= entry_costs['total_costs']
                            
                            open_position = {
                                'entry_idx': i,
                                'entry_date': current_date.strftime('%Y-%m-%d'),
                                'entry_price': entry_costs['adjusted_price'],
                                'shares': shares,
                                'signal_strength': signal_strength,
                                'entry_costs': entry_costs['total_costs']
                            }
                            
                            self.current_positions[symbol] = open_position
                            
                            # Record signal
                            self.signals.append({
                                'symbol': symbol,
                                'date': current_date.strftime('%Y-%m-%d'),
                                'price': entry_costs['adjusted_price'],
                                'signal_strength': signal_strength
                            })
            
            i += 1

    async def run_backtest(self, months: int = 6):
        """Run realistic backtest"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=months * 30)
        
        start_str = start_date.strftime('%Y-%m-%d')
        end_str = end_date.strftime('%Y-%m-%d')
        
        print(f"🚀 REALISTIC BACKTEST WITH ENHANCED CONDITIONS")
        print(f"📅 Period: {start_str} to {end_str}")
        print(f"💰 Starting Capital: ${self.starting_capital:,}")
        print(f"📊 Testing {len(self.stocks)} stocks")
        print(f"⚙️  Enhanced with: Trading Costs, Slippage, Risk Management")
        print("=" * 70)
        
        # Process stocks
        batch_size = 10
        for i in range(0, len(self.stocks), batch_size):
            batch = self.stocks[i:i + batch_size]
            print(f"Processing batch {i//batch_size + 1}: {batch[0]} to {batch[-1]}")
            
            tasks = [self.backtest_symbol(symbol, start_str, end_str) for symbol in batch]
            await asyncio.gather(*tasks, return_exceptions=True)
            
            await asyncio.sleep(1.0)
        
        return self.generate_results()

    def generate_results(self):
        """Generate comprehensive realistic results"""
        if not self.trades:
            print("❌ No trades executed!")
            return {}
        
        # Use performance analyzer
        analyzer = PerformanceAnalyzer(self.trades, self.starting_capital, self.current_capital)
        
        # Print performance summary
        analyzer.print_performance_summary()
        
        # Generate comprehensive report
        report = analyzer.generate_comprehensive_report()
        
        # Trading costs analysis
        if 'trading_costs_analysis' in report and report['trading_costs_analysis']:
            costs = report['trading_costs_analysis']
            print(f"\n💸 REALISTIC TRADING COSTS IMPACT:")
            print(f"  Total Costs: ${costs['total_trading_costs']:.0f}")
            print(f"  Cost per Trade: ${costs['avg_cost_per_trade']:.2f}")
            print(f"  Cost Impact: {costs['cost_impact_pct']:.2f}% of capital")
        
        # Save results
        results = {
            'strategy': 'Realistic_Backtester',
            'parameters': {
                'max_position_size': self.max_position_size,
                'max_concurrent_positions': self.max_concurrent_positions,
                'starting_capital': self.starting_capital,
                'ending_capital': self.current_capital
            },
            'performance_report': report,
            'trades': [trade.__dict__ for trade in self.trades],
            'signals': self.signals
        }
        
        import json
        with open('realistic_backtest_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Results saved to: realistic_backtest_results.json")
        return results

async def main():
    """Run realistic backtest"""
    async with RealisticBacktester(starting_capital=30000) as backtester:
        results = await backtester.run_backtest(months=6)
        return results

if __name__ == "__main__":
    asyncio.run(main())
