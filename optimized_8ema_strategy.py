#!/usr/bin/env python3
"""
Optimized 8EMA Exit Strategy with Compound Reinvestment
- $30K starting capital with auto-reinvestment
- Primary exit on 8EMA break
- Optimized for better performance
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import List, Dict, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

@dataclass
class Trade:
    symbol: str
    entry_date: str
    entry_price: float
    exit_date: str
    exit_price: float
    position_size: int
    signal_strength: int
    profit_loss: float
    profit_loss_pct: float
    days_held: int
    exit_reason: str

class Optimized8EMAStrategy:
    def __init__(self, starting_capital: float = 30000):
        self.fmp_key = 'K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7'
        self.session = None
        
        # Capital Management with Compound Reinvestment
        self.starting_capital = starting_capital
        self.current_capital = starting_capital
        self.trades: List[Trade] = []
        self.signals = []
        
        # Optimized Parameters
        self.max_position_pct = 0.08  # 8% max per position
        self.min_signal_strength = 6  # Minimum 6/8 signals
        self.min_trade_gap = 2  # Days between trades on same symbol (reduce whipsaws)
        
        # Enhanced Stock Universe (focus on liquid, trending stocks)
        self.stocks = [
            # High Beta Tech (good for momentum)
            'NVDA', 'AMD', 'TSLA', 'META', 'GOOGL', 'AAPL', 'MSFT', 'AMZN',
            # Growth Stocks
            'AVGO', 'CRM', 'ORCL', 'ADBE', 'NFLX', 'INTU', 'NOW',
            # Momentum Favorites  
            'LRCX', 'AMAT', 'KLAC', 'ADI', 'TXN', 'QCOM',
            # Financial Momentum
            'JPM', 'BAC', 'GS', 'AXP', 'SCHW',
            # Industrial/Energy
            'CAT', 'BA', 'RTX', 'XOM', 'CVX', 'COP',
            # Healthcare Growth
            'UNH', 'JNJ', 'PFE', 'ABBV', 'TMO', 'DHR',
            # Consumer Discretionary
            'HD', 'LOW', 'NKE', 'DIS', 'MCD',
            # Communication
            'VZ', 'T', 'TMUS', 'CMCSA'
        ]
        
        # Track recent trades to avoid whipsaws
        self.recent_trades = {}

    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            connector=aiohttp.TCPConnector(limit=100)
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    def calculate_position_size(self, signal_strength: int, current_capital: float, price: float) -> int:
        """Optimized position sizing with compound reinvestment"""
        
        # Base allocation by signal strength (more aggressive for stronger signals)
        allocation_pct = {
            6: 0.04,   # 4% for 6/8 signals
            7: 0.06,   # 6% for 7/8 signals  
            8: 0.08    # 8% for 8/8 signals (max allocation)
        }
        
        base_allocation = allocation_pct.get(signal_strength, 0.03)
        
        # Calculate position value using current capital (compound effect)
        position_value = current_capital * base_allocation
        shares = int(position_value / price)
        
        return max(shares, 1) if shares > 0 else 0

    def calculate_indicators(self, df):
        """Calculate optimized technical indicators"""
        # EMAs (key for exits)
        df['ema8'] = df['close'].ewm(span=8).mean()
        df['ema21'] = df['close'].ewm(span=21).mean()
        df['ema50'] = df['close'].ewm(span=50).mean()
        
        # Bollinger Bands
        df['bb_middle'] = df['close'].rolling(20).mean()
        df['bb_std'] = df['close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (df['bb_std'] * 2)
        df['bb_lower'] = df['bb_middle'] - (df['bb_std'] * 2)
        
        # Keltner Channels
        df['atr14'] = df[['high', 'low', 'close']].apply(
            lambda x: max(x['high'] - x['low'], 
                         abs(x['high'] - x['close']), 
                         abs(x['low'] - x['close'])), axis=1
        ).rolling(14).mean()
        
        df['kc_middle'] = df['ema21']
        df['kc_upper'] = df['kc_middle'] + (df['atr14'] * 1.5)
        df['kc_lower'] = df['kc_middle'] - (df['atr14'] * 1.5)
        
        # TTM Squeeze
        df['squeeze'] = (df['bb_upper'] < df['kc_upper']) & (df['bb_lower'] > df['kc_lower'])
        
        # Momentum
        df['momentum'] = df['close'] - df['close'].rolling(20).mean()
        df['histogram'] = df['momentum'] - df['momentum'].shift(1)
        
        # Trend strength
        df['trend_strength'] = (df['ema8'] > df['ema21']).astype(int) + \
                              (df['ema21'] > df['ema50']).astype(int) + \
                              (df['close'] > df['ema8']).astype(int)
        
        return df

    async def fetch_historical_data(self, symbol, start_date, end_date):
        """Fetch historical data with error handling"""
        try:
            url = f"https://financialmodelingprep.com/api/v3/historical-price-full/{symbol}"
            params = {
                'apikey': self.fmp_key,
                'from': start_date,
                'to': end_date
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if 'historical' in data and len(data['historical']) > 50:
                        df = pd.DataFrame(data['historical'])
                        df['date'] = pd.to_datetime(df['date'])
                        return df.sort_values('date').reset_index(drop=True)
        except Exception as e:
            print(f"Error fetching {symbol}: {e}")
        return None

    def check_enhanced_entry_signal(self, df, idx):
        """Enhanced entry signal with better filtering"""
        if idx < 10:
            return False, 0
            
        current = df.iloc[idx]
        prev = df.iloc[idx-1]
        prev2 = df.iloc[idx-2]
        
        score = 0
        
        try:
            # 1. Strong trend alignment (EMA8 > EMA21 > EMA50)
            if current['trend_strength'] >= 2:
                score += 2
            
            # 2. Squeeze firing or momentum building
            if (prev['squeeze'] and not current['squeeze']) or current['histogram'] > prev['histogram']:
                score += 1
            
            # 3. Price above 8EMA with momentum
            if current['close'] > current['ema8'] and current['momentum'] > 0:
                score += 2
            
            # 4. Volume confirmation (if available)
            if 'volume' in current and 'volume' in prev:
                if current['volume'] > prev['volume'] * 1.1:
                    score += 1
            else:
                score += 1
            
            # 5. Breakout above recent resistance
            recent_high = df.iloc[max(0, idx-5):idx]['high'].max()
            if current['close'] > recent_high * 1.005:  # 0.5% breakout
                score += 1
            
            # 6. ATR filter (avoid low volatility periods)
            if current['atr14'] > df['atr14'].rolling(20).mean().iloc[idx] * 0.8:
                score += 1
                
        except Exception as e:
            print(f"Error in entry signal calculation: {e}")
            return False, 0
        
        return score >= self.min_signal_strength, score

    def check_8ema_exit(self, df, entry_idx, current_idx):
        """Optimized 8EMA exit with additional filters"""
        if current_idx <= entry_idx:
            return False, "No exit"
        
        current = df.iloc[current_idx]
        entry = df.iloc[entry_idx]
        
        # Primary Exit: 8EMA break
        if current['close'] < current['ema8']:
            # Additional confirmation: ensure it's not just a brief dip
            if current_idx > entry_idx + 1:
                prev = df.iloc[current_idx-1]
                if prev['close'] < prev['ema8']:  # Two consecutive closes below 8EMA
                    return True, "8EMA confirmed break"
            return True, "8EMA break"
        
        # Profit protection: Take profits on large moves
        profit_pct = (current['close'] / entry['close'] - 1) * 100
        if profit_pct > 15:  # 15% profit
            return True, "Large profit protection"
        
        # Maximum hold period (prevent dead money)
        days_held = current_idx - entry_idx
        if days_held >= 15:
            return True, "Max hold period"
        
        return False, "Hold"

    async def backtest_symbol(self, symbol, start_date, end_date):
        """Backtest single symbol with enhanced logic"""
        df = await self.fetch_historical_data(symbol, start_date, end_date)
        if df is None or len(df) < 60:
            return
        
        df = self.calculate_indicators(df)
        
        i = 50  # Start after indicators are calculated
        open_position = None
        
        while i < len(df):
            current_date = df.iloc[i]['date']
            
            # Check for exit if we have an open position
            if open_position:
                should_exit, exit_reason = self.check_8ema_exit(df, open_position['entry_idx'], i)
                
                if should_exit:
                    # Close position with compound reinvestment
                    exit_price = df.iloc[i]['close']
                    days_held = i - open_position['entry_idx']
                    
                    # Calculate P&L
                    profit_loss = (exit_price - open_position['entry_price']) * open_position['shares']
                    profit_loss_pct = (exit_price / open_position['entry_price'] - 1) * 100
                    
                    trade = Trade(
                        symbol=symbol,
                        entry_date=open_position['entry_date'],
                        entry_price=open_position['entry_price'],
                        exit_date=current_date.strftime('%Y-%m-%d'),
                        exit_price=exit_price,
                        position_size=open_position['shares'],
                        signal_strength=open_position['signal_strength'],
                        profit_loss=profit_loss,
                        profit_loss_pct=profit_loss_pct,
                        days_held=days_held,
                        exit_reason=exit_reason
                    )
                    
                    self.trades.append(trade)
                    
                    # COMPOUND REINVESTMENT: Add/subtract P&L to capital
                    self.current_capital += profit_loss
                    
                    # Track recent trade to avoid immediate re-entry
                    self.recent_trades[symbol] = i
                    
                    print(f"TRADE: {symbol} {profit_loss_pct:+.1f}% ({exit_reason}) - Capital: ${self.current_capital:,.0f}")
                    
                    open_position = None
            
            # Check for new entry signal
            if not open_position and self.current_capital > 1000:
                # Avoid whipsaw trades
                if symbol in self.recent_trades:
                    if i - self.recent_trades[symbol] < self.min_trade_gap:
                        i += 1
                        continue
                
                has_signal, signal_strength = self.check_enhanced_entry_signal(df, i)
                
                if has_signal:
                    entry_price = df.iloc[i]['close']
                    shares = self.calculate_position_size(signal_strength, self.current_capital, entry_price)
                    
                    if shares > 0:
                        position_cost = shares * entry_price
                        
                        if position_cost <= self.current_capital * 0.95:  # Keep 5% cash buffer
                            open_position = {
                                'entry_idx': i,
                                'entry_date': current_date.strftime('%Y-%m-%d'),
                                'entry_price': entry_price,
                                'shares': shares,
                                'signal_strength': signal_strength
                            }
                            
                            # Record signal
                            self.signals.append({
                                'symbol': symbol,
                                'date': current_date.strftime('%Y-%m-%d'),
                                'price': entry_price,
                                'signal_strength': signal_strength,
                                'capital_at_entry': self.current_capital
                            })
            
            i += 1

    async def run_backtest(self, months: int = 6):
        """Run optimized backtest with compound reinvestment"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=months * 30)
        
        start_str = start_date.strftime('%Y-%m-%d')
        end_str = end_date.strftime('%Y-%m-%d')
        
        print(f"🚀 OPTIMIZED 8EMA STRATEGY WITH COMPOUND REINVESTMENT")
        print(f"📅 Period: {start_str} to {end_str}")
        print(f"💰 Starting Capital: ${self.starting_capital:,}")
        print(f"📊 Testing {len(self.stocks)} optimized stocks")
        print(f"🎯 Primary Exit: 8EMA break")
        print(f"💹 Auto-Reinvestment: Enabled")
        print("=" * 70)
        
        # Process stocks
        batch_size = 10
        for i in range(0, len(self.stocks), batch_size):
            batch = self.stocks[i:i + batch_size]
            print(f"Processing batch {i//batch_size + 1}: {batch[0]} to {batch[-1]}")
            
            tasks = [self.backtest_symbol(symbol, start_str, end_str) for symbol in batch]
            await asyncio.gather(*tasks, return_exceptions=True)
            
            await asyncio.sleep(1.0)  # Rate limiting
        
        return self.generate_results()

    def generate_results(self):
        """Generate comprehensive results"""
        if not self.trades:
            print("❌ No trades executed!")
            return {}
        
        # Calculate metrics
        total_trades = len(self.trades)
        winning_trades = [t for t in self.trades if t.profit_loss > 0]
        losing_trades = [t for t in self.trades if t.profit_loss < 0]
        
        win_rate = len(winning_trades) / total_trades * 100
        total_return_pct = (self.current_capital / self.starting_capital - 1) * 100
        total_profit_loss = self.current_capital - self.starting_capital
        
        avg_win = np.mean([t.profit_loss_pct for t in winning_trades]) if winning_trades else 0
        avg_loss = np.mean([t.profit_loss_pct for t in losing_trades]) if losing_trades else 0
        avg_hold_days = np.mean([t.days_held for t in self.trades])
        
        best_trade = max(self.trades, key=lambda t: t.profit_loss_pct)
        worst_trade = min(self.trades, key=lambda t: t.profit_loss_pct)
        
        # Display results
        print("\n" + "=" * 70)
        print("🎯 OPTIMIZED 8EMA STRATEGY RESULTS")
        print("=" * 70)
        
        print(f"💰 Starting Capital: ${self.starting_capital:,}")
        print(f"💰 Ending Capital: ${self.current_capital:,}")
        print(f"📈 Total Return: {total_return_pct:+.2f}%")
        print(f"💵 Net Profit/Loss: ${total_profit_loss:+,.0f}")
        print(f"🔢 Total Trades: {total_trades}")
        print(f"✅ Win Rate: {win_rate:.1f}%")
        print(f"📊 Average Win: {avg_win:+.2f}%")
        print(f"📊 Average Loss: {avg_loss:+.2f}%")
        print(f"⏱️  Average Hold: {avg_hold_days:.1f} days")
        
        print(f"\n🏆 BEST/WORST:")
        print(f"🥇 Best Trade: {best_trade.symbol} ({best_trade.profit_loss_pct:+.2f}%)")
        print(f"🥉 Worst Trade: {worst_trade.symbol} ({worst_trade.profit_loss_pct:+.2f}%)")
        
        # Compound growth analysis
        print(f"\n💹 COMPOUND GROWTH ANALYSIS:")
        print(f"📊 Capital Growth: ${self.starting_capital:,} → ${self.current_capital:,}")
        print(f"📈 Compound Return: {total_return_pct:+.2f}% over {len(self.trades)} trades")
        
        # Save results
        results = {
            'starting_capital': self.starting_capital,
            'ending_capital': self.current_capital,
            'total_return_pct': total_return_pct,
            'total_profit_loss': total_profit_loss,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'avg_win_pct': avg_win,
            'avg_loss_pct': avg_loss,
            'avg_hold_days': avg_hold_days,
            'best_trade': best_trade.__dict__,
            'worst_trade': worst_trade.__dict__,
            'trades': [trade.__dict__ for trade in self.trades],
            'signals': self.signals
        }
        
        with open('optimized_8ema_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Results saved to: optimized_8ema_results.json")
        return results

async def main():
    async with Optimized8EMAStrategy(starting_capital=30000) as strategy:
        results = await strategy.run_backtest(months=6)
        return results

if __name__ == "__main__":
    asyncio.run(main())
