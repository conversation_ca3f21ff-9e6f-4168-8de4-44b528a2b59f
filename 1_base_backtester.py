#!/usr/bin/env python3
"""
1. Base Backtester - Core backtesting framework
Foundation class with essential functionality
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import List, Dict, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

@dataclass
class Trade:
    symbol: str
    entry_date: str
    entry_price: float
    exit_date: str
    exit_price: float
    position_size: int
    signal_strength: int
    profit_loss: float
    profit_loss_pct: float
    days_held: int
    exit_reason: str
    trading_costs: float = 0.0

class BaseBacktester:
    def __init__(self, starting_capital: float = 30000):
        self.fmp_key = 'K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7'
        self.session = None
        
        # Capital Management
        self.starting_capital = starting_capital
        self.current_capital = starting_capital
        self.trades: List[Trade] = []
        self.signals = []
        self.current_positions = {}
        
        # Base Parameters
        self.max_position_pct = 0.08
        self.min_signal_strength = 6
        
        # Stock Universe
        self.stocks = [
            'AAPL', 'MSFT', 'AMZN', 'NVDA', 'GOOGL', 'TSLA', 'META',
            'AVGO', 'CRM', 'ORCL', 'ADBE', 'NFLX', 'INTU',
            'JPM', 'BAC', 'GS', 'AXP', 'V', 'MA',
            'UNH', 'JNJ', 'PFE', 'ABBV', 'TMO', 'DHR',
            'HD', 'LOW', 'NKE', 'DIS', 'MCD'
        ]

    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            connector=aiohttp.TCPConnector(limit=100)
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    async def fetch_historical_data(self, symbol, start_date, end_date):
        """Fetch historical data with error handling"""
        try:
            url = f"https://financialmodelingprep.com/api/v3/historical-price-full/{symbol}"
            params = {
                'apikey': self.fmp_key,
                'from': start_date,
                'to': end_date
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if 'historical' in data and len(data['historical']) > 50:
                        df = pd.DataFrame(data['historical'])
                        df['date'] = pd.to_datetime(df['date'])
                        return df.sort_values('date').reset_index(drop=True)
        except Exception as e:
            print(f"Error fetching {symbol}: {e}")
        return None

    def calculate_base_indicators(self, df):
        """Calculate basic technical indicators"""
        # EMAs
        df['ema5'] = df['close'].ewm(span=5).mean()
        df['ema8'] = df['close'].ewm(span=8).mean()
        df['ema21'] = df['close'].ewm(span=21).mean()
        df['ema50'] = df['close'].ewm(span=50).mean()
        
        # Bollinger Bands
        df['bb_middle'] = df['close'].rolling(20).mean()
        df['bb_std'] = df['close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (df['bb_std'] * 2)
        df['bb_lower'] = df['bb_middle'] - (df['bb_std'] * 2)
        
        # ATR
        df['high_low'] = df['high'] - df['low']
        df['high_close'] = abs(df['high'] - df['close'].shift(1))
        df['low_close'] = abs(df['low'] - df['close'].shift(1))
        df['true_range'] = df[['high_low', 'high_close', 'low_close']].max(axis=1)
        df['atr14'] = df['true_range'].rolling(14).mean()
        
        # Keltner Channels
        df['kc_middle'] = df['ema21']
        df['kc_upper'] = df['kc_middle'] + (df['atr14'] * 1.5)
        df['kc_lower'] = df['kc_middle'] - (df['atr14'] * 1.5)
        
        # TTM Squeeze
        df['squeeze'] = (df['bb_upper'] < df['kc_upper']) & (df['bb_lower'] > df['kc_lower'])
        
        # Momentum
        df['sma20'] = df['close'].rolling(20).mean()
        df['momentum'] = df['close'] - df['sma20']
        df['histogram'] = df['momentum'] - df['momentum'].shift(1)
        
        # Volume
        if 'volume' in df.columns:
            df['volume_avg20'] = df['volume'].rolling(20).mean()
        
        return df

    def calculate_position_size(self, signal_strength: int, current_capital: float, price: float) -> int:
        """Basic position sizing"""
        allocation_pct = {
            6: 0.04,
            7: 0.06,
            8: 0.08
        }
        
        base_allocation = allocation_pct.get(signal_strength, 0.03)
        position_value = current_capital * base_allocation
        shares = int(position_value / price)
        
        return max(shares, 1) if shares > 0 else 0

    def check_basic_entry_signal(self, df, idx):
        """Basic entry signal - to be overridden"""
        if idx < 25:
            return False, 0
        
        current = df.iloc[idx]
        score = 0
        
        # Basic momentum check
        if current['close'] > current['ema8']:
            score += 1
        
        # Basic trend check
        if current['ema8'] > current['ema21']:
            score += 1
            
        return score >= 2, score

    def check_basic_exit_signal(self, df, entry_idx, current_idx):
        """Basic 8EMA exit"""
        if current_idx <= entry_idx:
            return False, "No exit"
        
        current = df.iloc[current_idx]
        
        # 8EMA break
        if current['close'] < current['ema8']:
            return True, "8EMA break"
        
        # Max hold period
        days_held = current_idx - entry_idx
        if days_held >= 20:
            return True, "Max hold"
        
        return False, "Hold"

    def calculate_basic_metrics(self):
        """Calculate basic performance metrics"""
        if not self.trades:
            return {}
        
        total_trades = len(self.trades)
        winning_trades = [t for t in self.trades if t.profit_loss > 0]
        losing_trades = [t for t in self.trades if t.profit_loss < 0]
        
        win_rate = len(winning_trades) / total_trades * 100
        total_return_pct = (self.current_capital / self.starting_capital - 1) * 100
        total_profit_loss = self.current_capital - self.starting_capital
        
        avg_win = np.mean([t.profit_loss_pct for t in winning_trades]) if winning_trades else 0
        avg_loss = np.mean([t.profit_loss_pct for t in losing_trades]) if losing_trades else 0
        avg_hold_days = np.mean([t.days_held for t in self.trades])
        
        return {
            'starting_capital': self.starting_capital,
            'ending_capital': self.current_capital,
            'total_return_pct': total_return_pct,
            'total_profit_loss': total_profit_loss,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'avg_win_pct': avg_win,
            'avg_loss_pct': avg_loss,
            'avg_hold_days': avg_hold_days
        }

    async def run_backtest(self, months: int = 6):
        """Run basic backtest"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=months * 30)
        
        start_str = start_date.strftime('%Y-%m-%d')
        end_str = end_date.strftime('%Y-%m-%d')
        
        print(f"🚀 BASE BACKTESTER")
        print(f"📅 Period: {start_str} to {end_str}")
        print(f"💰 Starting Capital: ${self.starting_capital:,}")
        print(f"📊 Testing {len(self.stocks)} stocks")
        print("=" * 50)
        
        # Process stocks in batches
        batch_size = 10
        for i in range(0, len(self.stocks), batch_size):
            batch = self.stocks[i:i + batch_size]
            print(f"Processing batch {i//batch_size + 1}: {batch[0]} to {batch[-1]}")
            
            tasks = [self.backtest_symbol(symbol, start_str, end_str) for symbol in batch]
            await asyncio.gather(*tasks, return_exceptions=True)
            
            await asyncio.sleep(1.0)
        
        return self.generate_results()

    async def backtest_symbol(self, symbol, start_date, end_date):
        """Backtest single symbol - to be overridden"""
        df = await self.fetch_historical_data(symbol, start_date, end_date)
        if df is None or len(df) < 60:
            return
        
        df = self.calculate_base_indicators(df)
        
        # Basic backtesting logic here
        # This is a template - override in subclasses
        pass

    def generate_results(self):
        """Generate basic results"""
        metrics = self.calculate_basic_metrics()
        
        if not self.trades:
            print("❌ No trades executed!")
            return metrics
        
        print(f"\n💰 Starting Capital: ${metrics['starting_capital']:,}")
        print(f"💰 Ending Capital: ${metrics['ending_capital']:,}")
        print(f"📈 Total Return: {metrics['total_return_pct']:+.2f}%")
        print(f"🔢 Total Trades: {metrics['total_trades']}")
        print(f"✅ Win Rate: {metrics['win_rate']:.1f}%")
        print(f"📊 Average Win: {metrics['avg_win_pct']:+.2f}%")
        print(f"📊 Average Loss: {metrics['avg_loss_pct']:+.2f}%")
        
        return metrics

if __name__ == "__main__":
    async def main():
        async with BaseBacktester(starting_capital=30000) as backtester:
            results = await backtester.run_backtest(months=6)
            return results
    
    asyncio.run(main())
