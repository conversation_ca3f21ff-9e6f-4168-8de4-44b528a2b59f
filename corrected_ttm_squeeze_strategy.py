#!/usr/bin/env python3
"""
CORRECTED TTM-Squeeze Strategy Implementation
Matches the exact 8-criteria specification provided
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import List, Dict, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

@dataclass
class Trade:
    symbol: str
    entry_date: str
    entry_price: float
    exit_date: str
    exit_price: float
    position_size: int
    signal_strength: int
    profit_loss: float
    profit_loss_pct: float
    days_held: int
    exit_reason: str

class CorrectedTTMSqueezeStrategy:
    def __init__(self, starting_capital: float = 30000):
        self.fmp_key = 'K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7'
        self.session = None
        
        # Capital Management with Compound Reinvestment
        self.starting_capital = starting_capital
        self.current_capital = starting_capital
        self.trades: List[Trade] = []
        self.signals = []
        
        # TTM-Squeeze Parameters
        self.min_signal_strength = 7  # Need 7/8 criteria (87.5% threshold)
        self.max_position_pct = 0.08  # 8% max per position
        
        # Stock Universe - Focus on liquid, trending stocks
        self.stocks = [
            # High Beta Tech (good for momentum breakouts)
            'NVDA', 'AMD', 'TSLA', 'META', 'GOOGL', 'AAPL', 'MSFT', 'AMZN',
            # Growth Stocks with good volatility
            'AVGO', 'CRM', 'ORCL', 'ADBE', 'NFLX', 'INTU', 'NOW',
            # Semiconductor momentum plays
            'LRCX', 'AMAT', 'KLAC', 'ADI', 'TXN', 'QCOM',
            # Financial momentum
            'JPM', 'BAC', 'GS', 'AXP', 'SCHW',
            # Industrial/Energy breakouts
            'CAT', 'BA', 'RTX', 'XOM', 'CVX', 'COP',
            # Healthcare growth
            'UNH', 'JNJ', 'PFE', 'ABBV', 'TMO', 'DHR',
            # Consumer momentum
            'HD', 'LOW', 'NKE', 'DIS', 'MCD'
        ]

    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            connector=aiohttp.TCPConnector(limit=100)
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    def calculate_linear_regression_slope(self, series, period, idx):
        """Calculate linear regression slope for momentum trend"""
        if idx < period:
            return 0
        
        y_values = series.iloc[idx-period+1:idx+1].values
        x_values = np.arange(len(y_values))
        
        if len(y_values) < 2:
            return 0
            
        # Calculate slope using least squares
        n = len(y_values)
        sum_x = np.sum(x_values)
        sum_y = np.sum(y_values)
        sum_xy = np.sum(x_values * y_values)
        sum_x2 = np.sum(x_values ** 2)
        
        denominator = n * sum_x2 - sum_x ** 2
        if denominator == 0:
            return 0
            
        slope = (n * sum_xy - sum_x * sum_y) / denominator
        return slope

    def calculate_indicators(self, df):
        """Calculate all technical indicators for TTM-Squeeze"""
        # EMAs (including EMA5 as specified)
        df['ema5'] = df['close'].ewm(span=5).mean()
        df['ema8'] = df['close'].ewm(span=8).mean()
        df['ema21'] = df['close'].ewm(span=21).mean()
        
        # Bollinger Bands (20-period, 2 std dev)
        df['bb_middle'] = df['close'].rolling(20).mean()
        df['bb_std'] = df['close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (df['bb_std'] * 2)
        df['bb_lower'] = df['bb_middle'] - (df['bb_std'] * 2)
        
        # ATR (14-period)
        df['high_low'] = df['high'] - df['low']
        df['high_close'] = abs(df['high'] - df['close'].shift(1))
        df['low_close'] = abs(df['low'] - df['close'].shift(1))
        df['true_range'] = df[['high_low', 'high_close', 'low_close']].max(axis=1)
        df['atr14'] = df['true_range'].rolling(14).mean()
        
        # Keltner Channels (21-period EMA, 1.5 ATR)
        df['kc_middle'] = df['ema21']
        df['kc_upper'] = df['kc_middle'] + (df['atr14'] * 1.5)
        df['kc_lower'] = df['kc_middle'] - (df['atr14'] * 1.5)
        
        # TTM Squeeze (BB inside KC)
        df['squeeze'] = (df['bb_upper'] < df['kc_upper']) & (df['bb_lower'] > df['kc_lower'])
        
        # Momentum (Linear Regression of close vs 20-period SMA)
        df['sma20'] = df['close'].rolling(20).mean()
        df['momentum'] = df['close'] - df['sma20']
        
        # Histogram (momentum change)
        df['histogram'] = df['momentum'] - df['momentum'].shift(1)
        
        # Volume moving average
        df['volume_avg20'] = df['volume'].rolling(20).mean()
        
        # BB/ATR Ratio
        df['bb_width'] = df['bb_upper'] - df['bb_lower']
        df['bb_atr_ratio'] = df['bb_width'] / df['atr14']
        
        return df

    async def fetch_historical_data(self, symbol, start_date, end_date):
        """Fetch historical data with error handling"""
        try:
            url = f"https://financialmodelingprep.com/api/v3/historical-price-full/{symbol}"
            params = {
                'apikey': self.fmp_key,
                'from': start_date,
                'to': end_date
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if 'historical' in data and len(data['historical']) > 50:
                        df = pd.DataFrame(data['historical'])
                        df['date'] = pd.to_datetime(df['date'])
                        return df.sort_values('date').reset_index(drop=True)
        except Exception as e:
            print(f"Error fetching {symbol}: {e}")
        return None

    def check_ttm_squeeze_signal(self, df, idx):
        """
        EXACT TTM-Squeeze Pattern Implementation
        8 Criteria - Need 7/8 (87.5% threshold)
        """
        if idx < 25:  # Need enough data for all calculations
            return False, 0
            
        current = df.iloc[idx]
        prev = df.iloc[idx-1]
        prev2 = df.iloc[idx-2]
        prev3 = df.iloc[idx-3]
        
        score = 0
        criteria_met = []
        
        try:
            # 1. Histogram Turning MORE Positive
            hist_values = [current['histogram'], prev['histogram'], prev2['histogram'], prev3['histogram']]
            if (hist_values[0] > hist_values[1] and 
                hist_values[0] > hist_values[2] and 
                hist_values[0] > hist_values[3] and 
                hist_values[0] > 0):
                score += 1
                criteria_met.append("Histogram+")
            
            # 2. EMA Alignment & Trending
            ema5_trending = current['ema5'] > prev['ema5']
            ema8_trending = current['ema8'] > prev['ema8']
            ema21_trending = current['ema21'] > prev['ema21']
            ema_aligned = current['ema8'] > current['ema21']
            
            if ema_aligned and ema5_trending and ema8_trending and ema21_trending:
                score += 1
                criteria_met.append("EMA_Align")
            
            # 3. TTM Squeeze (Optional - Always Passes)
            score += 1
            criteria_met.append("Squeeze_OK")
            
            # 4. ATR Declining
            if current['atr14'] < prev['atr14']:
                score += 1
                criteria_met.append("ATR_Decline")
            
            # 5. BB/ATR Ratio < 1.5
            if pd.notna(current['bb_atr_ratio']) and current['bb_atr_ratio'] < 1.5:
                score += 1
                criteria_met.append("BB_ATR<1.5")
            
            # 6. Momentum Increasing (Linear Regression Slope)
            momentum_slope = self.calculate_linear_regression_slope(df['momentum'], 20, idx)
            if momentum_slope > 0:
                score += 1
                criteria_met.append("Mom_Rising")
            
            # 7. Volume Above Average
            if pd.notna(current['volume_avg20']) and current['volume'] > current['volume_avg20']:
                score += 1
                criteria_met.append("Vol_Above")
            
            # 8. Price Above 8EMA
            if current['close'] > current['ema8']:
                score += 1
                criteria_met.append("Price>EMA8")
                
        except Exception as e:
            print(f"Error in TTM-Squeeze signal calculation: {e}")
            return False, 0
        
        # Debug output for strong signals
        if score >= 6:
            print(f"  Signal {score}/8: {', '.join(criteria_met)}")
        
        return score >= self.min_signal_strength, score

    def calculate_position_size(self, signal_strength: int, current_capital: float, price: float) -> int:
        """Position sizing with compound reinvestment"""
        
        # Base allocation by signal strength
        allocation_pct = {
            7: 0.06,   # 6% for 7/8 signals
            8: 0.08    # 8% for perfect 8/8 signals
        }
        
        base_allocation = allocation_pct.get(signal_strength, 0.04)
        
        # Calculate position value using current capital (compound effect)
        position_value = current_capital * base_allocation
        shares = int(position_value / price)
        
        return max(shares, 1) if shares > 0 else 0

    def check_8ema_exit(self, df, entry_idx, current_idx):
        """8EMA exit with enhancements"""
        if current_idx <= entry_idx:
            return False, "No exit"
        
        current = df.iloc[current_idx]
        entry = df.iloc[entry_idx]
        
        # Primary Exit: 8EMA break
        if current['close'] < current['ema8']:
            return True, "8EMA break"
        
        # Profit protection on large moves
        profit_pct = (current['close'] / entry['close'] - 1) * 100
        if profit_pct > 15:  # 15% profit protection
            return True, "Large profit protection"
        
        # Maximum hold period
        days_held = current_idx - entry_idx
        if days_held >= 20:
            return True, "Max hold period"
        
        return False, "Hold"

    async def backtest_symbol(self, symbol, start_date, end_date):
        """Backtest single symbol with corrected TTM-Squeeze logic"""
        df = await self.fetch_historical_data(symbol, start_date, end_date)
        if df is None or len(df) < 60:
            return
        
        df = self.calculate_indicators(df)
        
        i = 30  # Start after indicators are calculated
        open_position = None
        
        while i < len(df):
            current_date = df.iloc[i]['date']
            
            # Check for exit if we have an open position
            if open_position:
                should_exit, exit_reason = self.check_8ema_exit(df, open_position['entry_idx'], i)
                
                if should_exit:
                    # Close position
                    exit_price = df.iloc[i]['close']
                    days_held = i - open_position['entry_idx']
                    
                    # Calculate P&L
                    profit_loss = (exit_price - open_position['entry_price']) * open_position['shares']
                    profit_loss_pct = (exit_price / open_position['entry_price'] - 1) * 100
                    
                    trade = Trade(
                        symbol=symbol,
                        entry_date=open_position['entry_date'],
                        entry_price=open_position['entry_price'],
                        exit_date=current_date.strftime('%Y-%m-%d'),
                        exit_price=exit_price,
                        position_size=open_position['shares'],
                        signal_strength=open_position['signal_strength'],
                        profit_loss=profit_loss,
                        profit_loss_pct=profit_loss_pct,
                        days_held=days_held,
                        exit_reason=exit_reason
                    )
                    
                    self.trades.append(trade)
                    
                    # COMPOUND REINVESTMENT
                    self.current_capital += profit_loss
                    
                    print(f"TRADE: {symbol} {profit_loss_pct:+.1f}% ({exit_reason}) - Capital: ${self.current_capital:,.0f}")
                    
                    open_position = None
            
            # Check for new entry signal
            if not open_position and self.current_capital > 1000:
                has_signal, signal_strength = self.check_ttm_squeeze_signal(df, i)
                
                if has_signal:
                    entry_price = df.iloc[i]['close']
                    shares = self.calculate_position_size(signal_strength, self.current_capital, entry_price)
                    
                    if shares > 0:
                        position_cost = shares * entry_price
                        
                        if position_cost <= self.current_capital * 0.95:
                            open_position = {
                                'entry_idx': i,
                                'entry_date': current_date.strftime('%Y-%m-%d'),
                                'entry_price': entry_price,
                                'shares': shares,
                                'signal_strength': signal_strength
                            }
                            
                            # Record signal
                            self.signals.append({
                                'symbol': symbol,
                                'date': current_date.strftime('%Y-%m-%d'),
                                'price': entry_price,
                                'signal_strength': signal_strength,
                                'capital_at_entry': self.current_capital
                            })
            
            i += 1

    async def run_backtest(self, months: int = 6):
        """Run corrected TTM-Squeeze backtest"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=months * 30)
        
        start_str = start_date.strftime('%Y-%m-%d')
        end_str = end_date.strftime('%Y-%m-%d')
        
        print(f"🎯 CORRECTED TTM-SQUEEZE STRATEGY")
        print(f"📅 Period: {start_str} to {end_str}")
        print(f"💰 Starting Capital: ${self.starting_capital:,}")
        print(f"📊 Testing {len(self.stocks)} stocks")
        print(f"🔍 Criteria: 7/8 TTM-Squeeze signals required")
        print(f"🎯 Exit: 8EMA break")
        print("=" * 70)
        
        # Process stocks
        batch_size = 8
        for i in range(0, len(self.stocks), batch_size):
            batch = self.stocks[i:i + batch_size]
            print(f"Processing batch {i//batch_size + 1}: {batch[0]} to {batch[-1]}")
            
            tasks = [self.backtest_symbol(symbol, start_str, end_str) for symbol in batch]
            await asyncio.gather(*tasks, return_exceptions=True)
            
            await asyncio.sleep(1.0)
        
        return self.generate_results()

    def generate_results(self):
        """Generate comprehensive results"""
        if not self.trades:
            print("❌ No trades executed!")
            return {}
        
        # Calculate metrics
        total_trades = len(self.trades)
        winning_trades = [t for t in self.trades if t.profit_loss > 0]
        losing_trades = [t for t in self.trades if t.profit_loss < 0]
        
        win_rate = len(winning_trades) / total_trades * 100
        total_return_pct = (self.current_capital / self.starting_capital - 1) * 100
        total_profit_loss = self.current_capital - self.starting_capital
        
        avg_win = np.mean([t.profit_loss_pct for t in winning_trades]) if winning_trades else 0
        avg_loss = np.mean([t.profit_loss_pct for t in losing_trades]) if losing_trades else 0
        avg_hold_days = np.mean([t.days_held for t in self.trades])
        
        best_trade = max(self.trades, key=lambda t: t.profit_loss_pct)
        worst_trade = min(self.trades, key=lambda t: t.profit_loss_pct)
        
        # Signal strength analysis
        signal_7_trades = [t for t in self.trades if t.signal_strength == 7]
        signal_8_trades = [t for t in self.trades if t.signal_strength == 8]
        
        # Display results
        print("\n" + "=" * 70)
        print("🎯 CORRECTED TTM-SQUEEZE RESULTS")
        print("=" * 70)
        
        print(f"💰 Starting Capital: ${self.starting_capital:,}")
        print(f"💰 Ending Capital: ${self.current_capital:,}")
        print(f"📈 Total Return: {total_return_pct:+.2f}%")
        print(f"💵 Net Profit/Loss: ${total_profit_loss:+,.0f}")
        print(f"🔢 Total Trades: {total_trades}")
        print(f"✅ Win Rate: {win_rate:.1f}%")
        print(f"📊 Average Win: {avg_win:+.2f}%")
        print(f"📊 Average Loss: {avg_loss:+.2f}%")
        print(f"⏱️  Average Hold: {avg_hold_days:.1f} days")
        
        print(f"\n🏆 BEST/WORST:")
        print(f"🥇 Best Trade: {best_trade.symbol} ({best_trade.profit_loss_pct:+.2f}%)")
        print(f"🥉 Worst Trade: {worst_trade.symbol} ({worst_trade.profit_loss_pct:+.2f}%)")
        
        print(f"\n🎯 SIGNAL STRENGTH ANALYSIS:")
        if signal_7_trades:
            win_rate_7 = len([t for t in signal_7_trades if t.profit_loss > 0]) / len(signal_7_trades) * 100
            avg_return_7 = np.mean([t.profit_loss_pct for t in signal_7_trades])
            print(f"  7/8 signals: {len(signal_7_trades)} trades, {win_rate_7:.1f}% win rate, {avg_return_7:+.2f}% avg return")
        
        if signal_8_trades:
            win_rate_8 = len([t for t in signal_8_trades if t.profit_loss > 0]) / len(signal_8_trades) * 100
            avg_return_8 = np.mean([t.profit_loss_pct for t in signal_8_trades])
            print(f"  8/8 signals: {len(signal_8_trades)} trades, {win_rate_8:.1f}% win rate, {avg_return_8:+.2f}% avg return")
        
        # Save results
        results = {
            'starting_capital': self.starting_capital,
            'ending_capital': self.current_capital,
            'total_return_pct': total_return_pct,
            'total_profit_loss': total_profit_loss,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'avg_win_pct': avg_win,
            'avg_loss_pct': avg_loss,
            'avg_hold_days': avg_hold_days,
            'best_trade': best_trade.__dict__,
            'worst_trade': worst_trade.__dict__,
            'trades': [trade.__dict__ for trade in self.trades],
            'signals': self.signals
        }
        
        with open('corrected_ttm_squeeze_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Results saved to: corrected_ttm_squeeze_results.json")
        return results

async def main():
    async with CorrectedTTMSqueezeStrategy(starting_capital=30000) as strategy:
        results = await strategy.run_backtest(months=6)
        return results

if __name__ == "__main__":
    asyncio.run(main())
