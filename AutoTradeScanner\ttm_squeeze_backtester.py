"""
TTM-Squeeze Strategy Backtester with Dynamic Position Sizing
Tests 6-month performance with $30K starting capital
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
import json
import os
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import List, Dict, Optional

@dataclass
class Trade:
    symbol: str
    entry_date: str
    entry_price: float
    exit_date: str
    exit_price: float
    position_size: float
    signal_strength: int
    profit_loss: float
    profit_loss_pct: float
    days_held: int
    exit_reason: str

class TTMSqueezeBacktester:
    def __init__(self, starting_capital: float = 30000):
        self.fmp_key = os.getenv('FMP_API_KEY') or 'K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7'
        self.session = None

        # Risk Management Parameters
        self.max_position_size = 0.10  # Max 10% per position
        self.max_portfolio_risk = 0.20  # Max 20% total risk
        self.max_daily_loss = 0.05  # Max 5% daily loss
        self.max_concurrent_positions = 10  # Max 10 positions at once
        self.correlation_limit = 0.7  # Max correlation between positions

        # Trading Costs
        self.commission_per_share = 0.005  # $0.005 per share
        self.min_commission = 1.0  # Minimum $1 commission
        self.sec_fee_rate = 0.0000221  # SEC fee rate
        self.slippage_base = 0.001  # Base slippage 0.1%

        # Track daily performance for risk management
        self.daily_pnl = {}
        self.current_positions = {}  # Track open positions
        self.starting_capital = starting_capital
        self.current_capital = starting_capital
        
        # Diversified stock universe to reduce survivorship bias
        # Mix of large/mid/small cap, different sectors, including some volatile stocks
        self.stocks = [
            # Large Cap Tech Leaders
            'AAPL', 'MSFT', 'AMZN', 'NVDA', 'GOOGL', 'META', 'TSLA', 'ORCL', 'CRM', 'ADBE',

            # Large Cap Traditional Blue Chips
            'JNJ', 'JPM', 'V', 'PG', 'XOM', 'HD', 'CVX', 'MA', 'PFE', 'ABBV', 'KO', 'WMT',
            'BAC', 'UNH', 'VZ', 'T', 'DIS', 'NKE', 'MCD', 'GE', 'IBM', 'CAT', 'GS',

            # Growth and Mid Cap
            'AVGO', 'AMD', 'CSCO', 'TMO', 'COST', 'DHR', 'TXN', 'NFLX', 'QCOM', 'RTX',
            'SCHW', 'HON', 'LOW', 'AMGN', 'SPGI', 'INTU', 'AXP', 'BKNG', 'NOW',

            # Cyclical and Value
            'COP', 'UPS', 'BMY', 'PM', 'NEE', 'DE', 'MDT', 'BA', 'FCX', 'CSX', 'MMC',
            'CI', 'PYPL', 'TMUS', 'ZTS', 'CVS', 'MU', 'TGT', 'MO', 'DUK', 'SO',

            # REITs and Specialized
            'AMT', 'EQIX', 'PLD', 'WM', 'APD', 'ICE', 'NSC', 'CME', 'EL',

            # Healthcare and Biotech
            'ISRG', 'VRTX', 'GILD', 'SYK', 'ADP', 'REGN', 'BSX', 'MDLZ',

            # Technology Hardware/Semiconductors
            'LRCX', 'AMAT', 'KLAC', 'ADI', 'INTC',

            # Financial Services
            'USB', 'PNC', 'CB', 'FIS', 'AON', 'PGR', 'ITW', 'CL',

            # Higher Risk/Volatile Stocks (to simulate real conditions)
            'SNAP', 'UBER', 'ROKU', 'ZM', 'DOCU', 'PLTR', 'COIN'
        ]
        
        self.trades: List[Trade] = []
        self.signals = []

    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            connector=aiohttp.TCPConnector(limit=50)
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    def calculate_indicators(self, df):
        """Calculate TTM-Squeeze indicators"""
        if len(df) < 30:
            return df
        
        # EMAs
        df['ema5'] = df['close'].ewm(span=5).mean()
        df['ema8'] = df['close'].ewm(span=8).mean()
        df['ema21'] = df['close'].ewm(span=21).mean()
        
        # Momentum histogram
        def calc_slope(prices):
            if len(prices) < 20:
                return 0
            x = np.arange(len(prices))
            return np.polyfit(x, prices, 1)[0]
        
        df['momentum'] = df['close'].rolling(20).apply(calc_slope, raw=True)
        df['histogram'] = df['momentum'].diff()
        
        # ATR
        df['tr'] = np.maximum(df['high'] - df['low'], 
                             np.maximum(abs(df['high'] - df['close'].shift(1)), 
                                       abs(df['low'] - df['close'].shift(1))))
        df['atr14'] = df['tr'].rolling(14).mean()
        
        # Bollinger Bands
        df['bb_sma'] = df['close'].rolling(20).mean()
        df['bb_std'] = df['close'].rolling(20).std()
        df['bb_width'] = df['bb_std'] * 4
        df['bb_atr_ratio'] = df['bb_width'] / df['atr14']
        
        # Volume
        df['vol_avg'] = df['volume'].rolling(20).mean()
        df['vol_ratio'] = df['volume'] / df['vol_avg']
        
        return df

    def check_entry_signal(self, df, i):
        """Check for TTM-Squeeze entry signal"""
        if i < 5:
            return False, 0
        
        current = df.iloc[i]
        prev = df.iloc[i-1]
        prev2 = df.iloc[i-2]
        prev3 = df.iloc[i-3]
        
        score = 0
        
        try:
            # 1. Histogram improving
            hist_values = [current['histogram'], prev['histogram'], prev2['histogram'], prev3['histogram']]
            if (hist_values[0] > hist_values[1] and hist_values[0] > hist_values[2] and 
                hist_values[0] > hist_values[3] and hist_values[0] > 0):
                score += 1
            
            # 2. EMA8 > EMA21 and trending up
            if (current['ema8'] > current['ema21'] and 
                current['ema8'] > prev['ema8'] and current['ema21'] > prev['ema21']):
                score += 1
            
            # 3. Squeeze optional (auto pass)
            score += 1
            
            # 4. ATR declining
            if current['atr14'] < prev['atr14']:
                score += 1
            
            # 5. BB/ATR ratio < 1.5
            if current['bb_atr_ratio'] < 1.5:
                score += 1
            
            # 6. Momentum increasing
            if current['momentum'] > prev['momentum']:
                score += 1
            
            # 7. Volume above average
            if current['vol_ratio'] > 1.0:
                score += 1
            
            # 8. Price above EMA8
            if current['close'] > current['ema8']:
                score += 1
                
        except:
            pass
        
        return score >= 6, score

    def check_exit_signal(self, df, entry_idx, current_idx):
        """Check for exit conditions"""
        if current_idx <= entry_idx:
            return False, "No exit"

        current = df.iloc[current_idx]
        entry = df.iloc[entry_idx]

        # Exit conditions:
        # 1. Price drops below 8EMA (stop loss)
        if current['close'] < current['ema8']:
            return True, "8EMA stop"

        # 2. Take profit at 2 ATR move
        atr_target = entry['close'] + (entry['atr14'] * 2)
        if current['close'] >= atr_target:
            return True, "2 ATR target"

        # 3. Maximum hold period (30 days)
        days_held = current_idx - entry_idx
        if days_held >= 30:
            return True, "Max hold period"

        # 4. Momentum turns negative for 3 consecutive days
        if (current_idx >= entry_idx + 3 and
            current['momentum'] < 0 and
            df.iloc[current_idx-1]['momentum'] < 0 and
            df.iloc[current_idx-2]['momentum'] < 0):
            return True, "Momentum reversal"

        return False, "Hold"

    def calculate_trading_costs(self, shares: int, price: float) -> float:
        """Calculate realistic trading costs"""
        commission = max(shares * self.commission_per_share, self.min_commission)
        sec_fee = shares * price * self.sec_fee_rate
        return commission + sec_fee

    def calculate_slippage(self, price: float, volume: float = None, market_cap: float = None) -> float:
        """Calculate realistic slippage based on stock characteristics"""
        slippage = self.slippage_base

        # Adjust for market cap (smaller = more slippage)
        if market_cap and market_cap < 1e9:  # Small cap
            slippage *= 3
        elif market_cap and market_cap < 10e9:  # Mid cap
            slippage *= 1.5

        # Adjust for volume (lower = more slippage)
        if volume and volume < 1e6:  # Low volume
            slippage *= 2

        return price * (1 + slippage)

    def check_risk_limits(self, symbol: str, position_value: float) -> bool:
        """Check if position violates risk management rules"""
        # Check max position size
        if position_value > self.current_capital * self.max_position_size:
            return False

        # Check max concurrent positions
        if len(self.current_positions) >= self.max_concurrent_positions:
            return False

        # Check daily loss limit
        today = datetime.now().strftime('%Y-%m-%d')
        if today in self.daily_pnl and self.daily_pnl[today] < -self.current_capital * self.max_daily_loss:
            return False

        return True

    def calculate_position_size(self, signal_strength: int, current_capital: float, price: float,
                              atr: float = None, volatility: float = None) -> float:
        """Enhanced position sizing with risk management"""

        # Base allocation percentages by signal strength
        allocation_pct = {
            6: 0.03,  # 3% for 6/8 signals
            7: 0.05,  # 5% for 7/8 signals
            8: 0.08   # 8% for 8/8 signals (perfect)
        }

        base_allocation = allocation_pct.get(signal_strength, 0.02)

        # Adjust for volatility (higher vol = smaller position)
        if volatility and volatility > 0:
            vol_adjustment = min(1.0, 0.20 / volatility)  # Target 20% volatility
            base_allocation *= vol_adjustment

        # Ensure we don't exceed max position size
        base_allocation = min(base_allocation, self.max_position_size)

        position_value = current_capital * base_allocation

        # Check risk limits
        if not self.check_risk_limits("", position_value):
            return 0

        shares = int(position_value / price)

        return max(shares, 0)  # Ensure no negative shares

    async def fetch_historical_data(self, symbol, start_date, end_date):
        """Fetch historical data for backtesting period"""
        try:
            url = f"https://financialmodelingprep.com/api/v3/historical-price-full/{symbol}"
            params = {
                'apikey': self.fmp_key,
                'from': start_date,
                'to': end_date
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if 'historical' in data and len(data['historical']) > 50:
                        df = pd.DataFrame(data['historical'])
                        df['date'] = pd.to_datetime(df['date'])
                        return df.sort_values('date').reset_index(drop=True)
        except Exception as e:
            print(f"Error fetching {symbol}: {e}")
        return None

    async def backtest_symbol(self, symbol, start_date, end_date):
        """Backtest single symbol"""
        df = await self.fetch_historical_data(symbol, start_date, end_date)
        if df is None or len(df) < 50:
            return
        
        df = self.calculate_indicators(df)
        
        i = 25  # Start after indicators are calculated
        open_position = None
        
        while i < len(df):
            current_date = df.iloc[i]['date']
            
            # Check for exit if we have an open position
            if open_position:
                should_exit, exit_reason = self.check_exit_signal(df, open_position['entry_idx'], i)
                
                if should_exit:
                    # Close position with slippage
                    exit_price = df.iloc[i]['close']
                    volume = df.iloc[i].get('volume', None)
                    exit_price_with_slippage = self.calculate_slippage(exit_price, volume)

                    # Calculate exit trading costs
                    exit_costs = self.calculate_trading_costs(open_position['shares'], exit_price_with_slippage)

                    days_held = i - open_position['entry_idx']

                    # Calculate P&L including all costs
                    gross_pnl = (exit_price_with_slippage - open_position['entry_price']) * open_position['shares']
                    total_costs = open_position.get('entry_costs', 0) + exit_costs
                    net_pnl = gross_pnl - total_costs

                    profit_loss_pct = (exit_price_with_slippage / open_position['entry_price'] - 1) * 100

                    trade = Trade(
                        symbol=symbol,
                        entry_date=open_position['entry_date'],
                        entry_price=open_position['entry_price'],
                        exit_date=current_date.strftime('%Y-%m-%d'),
                        exit_price=exit_price_with_slippage,
                        position_size=open_position['shares'],
                        signal_strength=open_position['signal_strength'],
                        profit_loss=net_pnl,  # Net P&L after costs
                        profit_loss_pct=profit_loss_pct,
                        days_held=days_held,
                        exit_reason=exit_reason
                    )

                    self.trades.append(trade)
                    self.current_capital += net_pnl

                    # Update daily P&L tracking
                    today = current_date.strftime('%Y-%m-%d')
                    if today not in self.daily_pnl:
                        self.daily_pnl[today] = 0
                    self.daily_pnl[today] += net_pnl

                    print(f"TRADE: {symbol} {profit_loss_pct:+.1f}% ({exit_reason}) - Capital: ${self.current_capital:,.0f}")

                    # Remove from current positions
                    if symbol in self.current_positions:
                        del self.current_positions[symbol]

                    open_position = None
            
            # Check for new entry signal if no open position
            if not open_position:
                has_signal, signal_strength = self.check_entry_signal(df, i)
                
                if has_signal and self.current_capital > 1000:  # Minimum capital check
                    entry_price = df.iloc[i]['close']

                    # Calculate volatility for position sizing
                    volatility = df.iloc[i]['atr14'] / entry_price if 'atr14' in df.columns else None

                    shares = self.calculate_position_size(signal_strength, self.current_capital, entry_price,
                                                        volatility=volatility)

                    if shares > 0:
                        # Apply slippage to entry price
                        volume = df.iloc[i].get('volume', None)
                        entry_price_with_slippage = self.calculate_slippage(entry_price, volume)

                        # Calculate trading costs
                        trading_costs = self.calculate_trading_costs(shares, entry_price_with_slippage)

                        # Check if we have enough capital after costs
                        total_cost = shares * entry_price_with_slippage + trading_costs

                        if total_cost <= self.current_capital:
                            # Deduct costs from capital
                            self.current_capital -= trading_costs

                            open_position = {
                                'entry_idx': i,
                                'entry_date': current_date.strftime('%Y-%m-%d'),
                                'entry_price': entry_price_with_slippage,
                                'shares': shares,
                                'signal_strength': signal_strength,
                                'entry_costs': trading_costs
                            }

                            # Track position
                            self.current_positions[symbol] = open_position
                        
                        # Record signal for analysis
                        self.signals.append({
                            'symbol': symbol,
                            'date': current_date.strftime('%Y-%m-%d'),
                            'price': entry_price,
                            'signal_strength': signal_strength
                        })
            
            i += 1

    def calculate_performance_metrics(self):
        """Calculate comprehensive performance metrics with enhanced risk analysis"""
        if not self.trades:
            return {}

        # Basic metrics
        total_trades = len(self.trades)
        winning_trades = [t for t in self.trades if t.profit_loss > 0]
        losing_trades = [t for t in self.trades if t.profit_loss < 0]

        win_rate = len(winning_trades) / total_trades * 100
        loss_rate = len(losing_trades) / total_trades * 100

        total_profit_loss = sum(t.profit_loss for t in self.trades)
        total_return_pct = (self.current_capital / self.starting_capital - 1) * 100

        # Average metrics
        avg_win = np.mean([t.profit_loss_pct for t in winning_trades]) if winning_trades else 0
        avg_loss = np.mean([t.profit_loss_pct for t in losing_trades]) if losing_trades else 0
        avg_hold_days = np.mean([t.days_held for t in self.trades])

        # Risk metrics
        returns = [t.profit_loss_pct for t in self.trades]
        volatility = np.std(returns) if len(returns) > 1 else 0

        # Sharpe ratio (assuming 2% risk-free rate)
        risk_free_rate = 2.0
        excess_return = np.mean(returns) - (risk_free_rate / 252)  # Daily risk-free rate
        sharpe_ratio = excess_return / volatility if volatility > 0 else 0

        # Maximum drawdown
        cumulative_returns = np.cumsum([t.profit_loss for t in self.trades])
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdowns = (cumulative_returns - running_max) / self.starting_capital * 100
        max_drawdown = np.min(drawdowns) if len(drawdowns) > 0 else 0

        # Profit factor
        gross_profit = sum(t.profit_loss for t in winning_trades) if winning_trades else 0
        gross_loss = abs(sum(t.profit_loss for t in losing_trades)) if losing_trades else 1
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0

        # Best and worst trades
        best_trade = max(self.trades, key=lambda t: t.profit_loss_pct)
        worst_trade = min(self.trades, key=lambda t: t.profit_loss_pct)

        # Consecutive wins/losses
        consecutive_wins = 0
        consecutive_losses = 0
        max_consecutive_wins = 0
        max_consecutive_losses = 0

        for trade in self.trades:
            if trade.profit_loss > 0:
                consecutive_wins += 1
                consecutive_losses = 0
                max_consecutive_wins = max(max_consecutive_wins, consecutive_wins)
            else:
                consecutive_losses += 1
                consecutive_wins = 0
                max_consecutive_losses = max(max_consecutive_losses, consecutive_losses)

        # Signal strength analysis
        signal_stats = {}
        for strength in [6, 7, 8]:
            strength_trades = [t for t in self.trades if t.signal_strength == strength]
            if strength_trades:
                signal_stats[f'{strength}/8_signals'] = {
                    'count': len(strength_trades),
                    'win_rate': len([t for t in strength_trades if t.profit_loss > 0]) / len(strength_trades) * 100,
                    'avg_return': np.mean([t.profit_loss_pct for t in strength_trades])
                }

        return {
            'starting_capital': self.starting_capital,
            'ending_capital': self.current_capital,
            'total_return_pct': total_return_pct,
            'total_profit_loss': total_profit_loss,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'loss_rate': loss_rate,
            'avg_win_pct': avg_win,
            'avg_loss_pct': avg_loss,
            'avg_hold_days': avg_hold_days,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'profit_factor': profit_factor,
            'max_consecutive_wins': max_consecutive_wins,
            'max_consecutive_losses': max_consecutive_losses,
            'best_trade': {
                'symbol': best_trade.symbol,
                'return_pct': best_trade.profit_loss_pct,
                'profit': best_trade.profit_loss
            },
            'worst_trade': {
                'symbol': worst_trade.symbol,
                'return_pct': worst_trade.profit_loss_pct,
                'loss': worst_trade.profit_loss
            },
            'signal_strength_analysis': signal_stats
        }

    async def run_backtest(self, months: int = 6):
        """Run complete backtest"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=months * 30)
        
        start_str = start_date.strftime('%Y-%m-%d')
        end_str = end_date.strftime('%Y-%m-%d')
        
        print(f"TTM-Squeeze Strategy Backtest")
        print(f"Period: {start_str} to {end_str}")
        print(f"Starting Capital: ${self.starting_capital:,}")
        print(f"Testing {len(self.stocks)} stocks")
        print("=" * 60)
        
        # Process stocks in batches
        batch_size = 20
        for i in range(0, len(self.stocks), batch_size):
            batch = self.stocks[i:i + batch_size]
            print(f"Processing batch {i//batch_size + 1}: {batch[0]} to {batch[-1]}")
            
            tasks = [self.backtest_symbol(symbol, start_str, end_str) for symbol in batch]
            await asyncio.gather(*tasks, return_exceptions=True)
            
            await asyncio.sleep(1)  # Rate limiting
        
        # Calculate and display results
        metrics = self.calculate_performance_metrics()
        
        print("\n" + "=" * 60)
        print("BACKTEST RESULTS")
        print("=" * 60)
        
        if metrics:
            print(f"Starting Capital: ${metrics['starting_capital']:,}")
            print(f"Ending Capital: ${metrics['ending_capital']:,}")
            print(f"Total Return: {metrics['total_return_pct']:+.2f}%")
            print(f"Total Profit/Loss: ${metrics['total_profit_loss']:+,.0f}")
            print(f"Total Trades: {metrics['total_trades']}")
            print(f"Win Rate: {metrics['win_rate']:.1f}%")
            print(f"Loss Rate: {metrics['loss_rate']:.1f}%")
            print(f"Average Win: {metrics['avg_win_pct']:+.2f}%")
            print(f"Average Loss: {metrics['avg_loss_pct']:+.2f}%")
            print(f"Average Hold Days: {metrics['avg_hold_days']:.1f}")

            print(f"\nRisk Metrics:")
            print(f"Volatility: {metrics['volatility']:.2f}%")
            print(f"Sharpe Ratio: {metrics['sharpe_ratio']:.2f}")
            print(f"Max Drawdown: {metrics['max_drawdown']:.2f}%")
            print(f"Profit Factor: {metrics['profit_factor']:.2f}")
            print(f"Max Consecutive Wins: {metrics['max_consecutive_wins']}")
            print(f"Max Consecutive Losses: {metrics['max_consecutive_losses']}")

            print(f"\nBest Trade: {metrics['best_trade']['symbol']} ({metrics['best_trade']['return_pct']:+.2f}%)")
            print(f"Worst Trade: {metrics['worst_trade']['symbol']} ({metrics['worst_trade']['return_pct']:+.2f}%)")

            print("\nSignal Strength Analysis:")
            for signal_type, stats in metrics['signal_strength_analysis'].items():
                print(f"  {signal_type}: {stats['count']} trades, {stats['win_rate']:.1f}% win rate, {stats['avg_return']:+.2f}% avg return")
        
        # Save detailed results
        results = {
            'metrics': metrics,
            'trades': [trade.__dict__ for trade in self.trades],
            'signals': self.signals
        }
        
        with open('ttm_squeeze_backtest_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        return results

async def main():
    async with TTMSqueezeBacktester(starting_capital=30000) as backtester:
        results = await backtester.run_backtest(months=6)
        return results

if __name__ == "__main__":
    asyncio.run(main())

