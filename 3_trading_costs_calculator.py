#!/usr/bin/env python3
"""
3. Trading Costs Calculator - Realistic trading cost modeling
Includes commissions, slippage, SEC fees, and market impact
"""

import numpy as np
from typing import Optional

class TradingCostsCalculator:
    """
    Realistic trading costs calculation for backtesting
    Includes all major cost components that affect real trading
    """
    
    def __init__(self):
        # Commission Structure
        self.commission_per_share = 0.005  # $0.005 per share
        self.min_commission = 1.0          # $1 minimum commission
        self.max_commission = 5.0          # $5 maximum commission
        
        # SEC and Regulatory Fees
        self.sec_fee_rate = 0.0000231      # SEC fee rate (2.31 per $1M)
        self.finra_taf_rate = 0.000145     # FINRA TAF rate
        
        # Slippage Parameters
        self.slippage_base = 0.001         # 0.1% base slippage
        self.slippage_volume_factor = 0.5  # Volume impact factor
        self.slippage_volatility_factor = 0.3  # Volatility impact factor
        
        # Market Impact Parameters
        self.market_impact_threshold = 100000  # $100k position size threshold
        self.market_impact_rate = 0.0005       # 0.05% additional impact
        
    def calculate_commission(self, shares: int, price: float) -> float:
        """Calculate commission costs"""
        commission = shares * self.commission_per_share
        commission = max(commission, self.min_commission)
        commission = min(commission, self.max_commission)
        return commission
    
    def calculate_sec_fees(self, shares: int, price: float) -> float:
        """Calculate SEC and regulatory fees"""
        trade_value = shares * price
        sec_fee = trade_value * self.sec_fee_rate
        finra_fee = shares * self.finra_taf_rate
        return sec_fee + finra_fee
    
    def calculate_slippage(self, price: float, shares: int, 
                          volume: Optional[float] = None,
                          volatility: Optional[float] = None,
                          market_cap: Optional[float] = None,
                          is_exit: bool = False) -> float:
        """
        Calculate realistic slippage based on multiple factors
        
        Args:
            price: Stock price
            shares: Number of shares
            volume: Daily volume (optional)
            volatility: Stock volatility (optional) 
            market_cap: Market capitalization (optional)
            is_exit: Whether this is an exit trade
            
        Returns:
            Adjusted price after slippage
        """
        slippage = self.slippage_base
        
        # Volume impact
        if volume is not None:
            trade_volume_ratio = (shares * price) / (volume * price) if volume > 0 else 0
            if trade_volume_ratio > 0.01:  # Trade > 1% of daily volume
                slippage += trade_volume_ratio * self.slippage_volume_factor
        
        # Volatility impact
        if volatility is not None:
            if volatility > 0.03:  # High volatility (>3% daily)
                slippage += volatility * self.slippage_volatility_factor
        
        # Market cap impact (smaller stocks = more slippage)
        if market_cap is not None:
            if market_cap < 1e9:  # Small cap (<$1B)
                slippage *= 2.5
            elif market_cap < 10e9:  # Mid cap (<$10B)
                slippage *= 1.5
        
        # Exit trades typically have slightly higher slippage
        if is_exit:
            slippage *= 1.1
        
        # Apply slippage (positive for buys, negative for sells in terms of cost)
        if is_exit:
            return price * (1 - slippage)  # Lower price on exit
        else:
            return price * (1 + slippage)  # Higher price on entry
    
    def calculate_market_impact(self, shares: int, price: float) -> float:
        """Calculate market impact for large trades"""
        position_value = shares * price
        
        if position_value > self.market_impact_threshold:
            excess_value = position_value - self.market_impact_threshold
            impact = excess_value * self.market_impact_rate
            return impact
        
        return 0.0
    
    def calculate_total_trading_costs(self, shares: int, price: float,
                                    volume: Optional[float] = None,
                                    volatility: Optional[float] = None,
                                    market_cap: Optional[float] = None,
                                    is_exit: bool = False) -> dict:
        """
        Calculate all trading costs for a trade
        
        Returns:
            Dictionary with breakdown of all costs
        """
        # Base costs
        commission = self.calculate_commission(shares, price)
        sec_fees = self.calculate_sec_fees(shares, price)
        market_impact = self.calculate_market_impact(shares, price)
        
        # Slippage (price adjustment)
        adjusted_price = self.calculate_slippage(
            price, shares, volume, volatility, market_cap, is_exit
        )
        slippage_cost = abs(adjusted_price - price) * shares
        
        total_costs = commission + sec_fees + market_impact + slippage_cost
        
        return {
            'commission': commission,
            'sec_fees': sec_fees,
            'market_impact': market_impact,
            'slippage_cost': slippage_cost,
            'total_costs': total_costs,
            'adjusted_price': adjusted_price,
            'cost_percentage': (total_costs / (shares * price)) * 100
        }
    
    def get_cost_summary(self, entry_costs: dict, exit_costs: dict) -> dict:
        """Get summary of round-trip trading costs"""
        total_entry = entry_costs['total_costs']
        total_exit = exit_costs['total_costs']
        total_round_trip = total_entry + total_exit
        
        # Calculate as percentage of position size
        position_value = (entry_costs['adjusted_price'] * 
                         (entry_costs['total_costs'] / entry_costs['cost_percentage'] * 100))
        
        return {
            'entry_costs': total_entry,
            'exit_costs': total_exit,
            'total_round_trip': total_round_trip,
            'round_trip_percentage': (total_round_trip / position_value) * 100,
            'break_even_move': (total_round_trip / position_value) * 100
        }

class SimplifiedCosts:
    """Simplified cost model for quick backtesting"""
    
    def __init__(self):
        self.commission_per_trade = 5.0    # $5 per trade
        self.slippage_pct = 0.15           # 0.15% slippage
        
    def calculate_entry_costs(self, shares: int, price: float) -> float:
        """Calculate entry costs (commission + slippage)"""
        commission = self.commission_per_trade
        slippage = shares * price * (self.slippage_pct / 100)
        return commission + slippage
    
    def calculate_exit_costs(self, shares: int, price: float) -> float:
        """Calculate exit costs (commission + slippage)"""
        commission = self.commission_per_trade
        slippage = shares * price * (self.slippage_pct / 100)
        return commission + slippage
    
    def get_adjusted_prices(self, entry_price: float, exit_price: float) -> tuple:
        """Get slippage-adjusted prices"""
        adjusted_entry = entry_price * (1 + self.slippage_pct / 100)
        adjusted_exit = exit_price * (1 - self.slippage_pct / 100)
        return adjusted_entry, adjusted_exit

# Example usage and testing
if __name__ == "__main__":
    # Test realistic costs
    costs_calc = TradingCostsCalculator()
    
    # Example trade: 100 shares at $150
    shares = 100
    entry_price = 150.0
    exit_price = 165.0
    volume = 500000  # 500k daily volume
    volatility = 0.025  # 2.5% daily volatility
    market_cap = 5e9  # $5B market cap
    
    print("=== REALISTIC TRADING COSTS ANALYSIS ===")
    print(f"Trade: {shares} shares")
    print(f"Entry Price: ${entry_price:.2f}")
    print(f"Exit Price: ${exit_price:.2f}")
    print(f"Position Value: ${shares * entry_price:,.0f}")
    
    # Entry costs
    entry_costs = costs_calc.calculate_total_trading_costs(
        shares, entry_price, volume, volatility, market_cap, is_exit=False
    )
    
    print(f"\n📈 ENTRY COSTS:")
    print(f"  Commission: ${entry_costs['commission']:.2f}")
    print(f"  SEC Fees: ${entry_costs['sec_fees']:.2f}")
    print(f"  Market Impact: ${entry_costs['market_impact']:.2f}")
    print(f"  Slippage Cost: ${entry_costs['slippage_cost']:.2f}")
    print(f"  Total Entry: ${entry_costs['total_costs']:.2f}")
    print(f"  Adjusted Price: ${entry_costs['adjusted_price']:.2f}")
    
    # Exit costs
    exit_costs = costs_calc.calculate_total_trading_costs(
        shares, exit_price, volume, volatility, market_cap, is_exit=True
    )
    
    print(f"\n📉 EXIT COSTS:")
    print(f"  Commission: ${exit_costs['commission']:.2f}")
    print(f"  SEC Fees: ${exit_costs['sec_fees']:.2f}")
    print(f"  Market Impact: ${exit_costs['market_impact']:.2f}")
    print(f"  Slippage Cost: ${exit_costs['slippage_cost']:.2f}")
    print(f"  Total Exit: ${exit_costs['total_costs']:.2f}")
    print(f"  Adjusted Price: ${exit_costs['adjusted_price']:.2f}")
    
    # Round trip summary
    summary = costs_calc.get_cost_summary(entry_costs, exit_costs)
    
    print(f"\n💰 ROUND TRIP SUMMARY:")
    print(f"  Total Costs: ${summary['total_round_trip']:.2f}")
    print(f"  Cost Percentage: {summary['round_trip_percentage']:.3f}%")
    print(f"  Break-even Move: {summary['break_even_move']:.3f}%")
    
    # Calculate actual P&L
    gross_pnl = (exit_costs['adjusted_price'] - entry_costs['adjusted_price']) * shares
    net_pnl = gross_pnl - summary['total_round_trip']
    
    print(f"\n📊 P&L ANALYSIS:")
    print(f"  Gross P&L: ${gross_pnl:.2f}")
    print(f"  Net P&L: ${net_pnl:.2f}")
    print(f"  Return %: {(net_pnl / (shares * entry_price)) * 100:.2f}%")
    
    # Compare with simplified model
    print(f"\n=== SIMPLIFIED MODEL COMPARISON ===")
    simple_costs = SimplifiedCosts()
    simple_entry = simple_costs.calculate_entry_costs(shares, entry_price)
    simple_exit = simple_costs.calculate_exit_costs(shares, exit_price)
    simple_total = simple_entry + simple_exit
    
    print(f"Simplified Total Costs: ${simple_total:.2f}")
    print(f"Realistic Total Costs: ${summary['total_round_trip']:.2f}")
    print(f"Difference: ${summary['total_round_trip'] - simple_total:.2f}")
