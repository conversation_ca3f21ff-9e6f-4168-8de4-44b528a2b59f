#!/usr/bin/env python3
"""
Verify backtest results for accuracy
"""
import json

def verify_backtest_results():
    try:
        with open('ttm_squeeze_backtest_results.json', 'r') as f:
            data = json.load(f)
    except FileNotFoundError:
        print("❌ Results file not found!")
        return

    trades = data['trades']
    metrics = data['metrics']

    print('=== VERIFICATION ANALYSIS ===')
    print(f'Total trades in file: {len(trades)}')
    print(f'Reported total trades: {metrics["total_trades"]}')

    # Calculate actual P&L from trades
    total_pnl = sum(trade['profit_loss'] for trade in trades)
    print(f'Calculated total P&L: ${total_pnl:.2f}')
    print(f'Reported total P&L: ${metrics["total_profit_loss"]:.2f}')

    # Verify capital calculation
    starting_capital = metrics['starting_capital']
    ending_capital = starting_capital + total_pnl
    print(f'Calculated ending capital: ${ending_capital:.2f}')
    print(f'Reported ending capital: ${metrics["ending_capital"]:.2f}')

    # Check win/loss counts
    winning_trades = [t for t in trades if t['profit_loss'] > 0]
    losing_trades = [t for t in trades if t['profit_loss'] <= 0]
    print(f'Calculated winning trades: {len(winning_trades)}')
    print(f'Calculated losing trades: {len(losing_trades)}')
    print(f'Calculated win rate: {len(winning_trades)/len(trades)*100:.2f}%')
    print(f'Reported win rate: {metrics["win_rate"]:.2f}%')

    # Check average win/loss
    avg_win = sum(t['profit_loss_pct'] for t in winning_trades) / len(winning_trades) if winning_trades else 0
    avg_loss = sum(t['profit_loss_pct'] for t in losing_trades) / len(losing_trades) if losing_trades else 0
    print(f'Calculated avg win: {avg_win:.2f}%')
    print(f'Calculated avg loss: {avg_loss:.2f}%')
    print(f'Reported avg win: {metrics["avg_win_pct"]:.2f}%')
    print(f'Reported avg loss: {metrics["avg_loss_pct"]:.2f}%')

    # Check for discrepancies
    print('\n=== DISCREPANCY CHECK ===')
    discrepancies = []
    
    if abs(total_pnl - metrics["total_profit_loss"]) > 0.01:
        discrepancies.append(f"P&L mismatch: {total_pnl:.2f} vs {metrics['total_profit_loss']:.2f}")
    
    if abs(ending_capital - metrics["ending_capital"]) > 0.01:
        discrepancies.append(f"Capital mismatch: {ending_capital:.2f} vs {metrics['ending_capital']:.2f}")
    
    if len(trades) != metrics["total_trades"]:
        discrepancies.append(f"Trade count mismatch: {len(trades)} vs {metrics['total_trades']}")
    
    if abs(len(winning_trades)/len(trades)*100 - metrics["win_rate"]) > 0.01:
        discrepancies.append(f"Win rate mismatch: {len(winning_trades)/len(trades)*100:.2f}% vs {metrics['win_rate']:.2f}%")

    if discrepancies:
        print("❌ DISCREPANCIES FOUND:")
        for disc in discrepancies:
            print(f"  - {disc}")
    else:
        print("✅ ALL CALCULATIONS VERIFIED CORRECTLY")

    # Sample trade verification
    print('\n=== SAMPLE TRADE VERIFICATION ===')
    for i, trade in enumerate(trades[:5]):
        print(f"Trade {i+1}: {trade['symbol']}")
        print(f"  Entry: ${trade['entry_price']:.2f} x {trade['position_size']} shares")
        print(f"  Exit: ${trade['exit_price']:.2f}")
        print(f"  P&L: ${trade['profit_loss']:.2f} ({trade['profit_loss_pct']:.2f}%)")
        
        # Verify P&L calculation
        expected_pnl = (trade['exit_price'] - trade['entry_price']) * trade['position_size']
        expected_pct = (trade['exit_price'] / trade['entry_price'] - 1) * 100
        
        if abs(expected_pnl - trade['profit_loss']) > 0.01:
            print(f"  ❌ P&L Error: Expected ${expected_pnl:.2f}, got ${trade['profit_loss']:.2f}")
        if abs(expected_pct - trade['profit_loss_pct']) > 0.01:
            print(f"  ❌ % Error: Expected {expected_pct:.2f}%, got {trade['profit_loss_pct']:.2f}%")

if __name__ == "__main__":
    verify_backtest_results()
