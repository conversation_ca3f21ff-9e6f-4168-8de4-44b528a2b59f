#!/usr/bin/env python3
"""
5. Performance Metrics - Comprehensive performance analysis and reporting
Advanced metrics for strategy evaluation
"""

import numpy as np
import pandas as pd
import json
from typing import List, Dict, Any
from dataclasses import dataclass
from datetime import datetime
import math

@dataclass
class Trade:
    symbol: str
    entry_date: str
    entry_price: float
    exit_date: str
    exit_price: float
    position_size: int
    signal_strength: int
    profit_loss: float
    profit_loss_pct: float
    days_held: int
    exit_reason: str
    trading_costs: float = 0.0

class PerformanceAnalyzer:
    """
    Comprehensive performance analysis for trading strategies
    Calculates advanced metrics and risk-adjusted returns
    """
    
    def __init__(self, trades: List[Trade], starting_capital: float, ending_capital: float):
        self.trades = trades
        self.starting_capital = starting_capital
        self.ending_capital = ending_capital
        self.risk_free_rate = 0.02  # 2% annual risk-free rate
        
    def calculate_basic_metrics(self) -> Dict[str, Any]:
        """Calculate basic performance metrics"""
        if not self.trades:
            return {}
        
        total_trades = len(self.trades)
        winning_trades = [t for t in self.trades if t.profit_loss > 0]
        losing_trades = [t for t in self.trades if t.profit_loss < 0]
        
        win_rate = len(winning_trades) / total_trades * 100
        loss_rate = len(losing_trades) / total_trades * 100
        
        total_return_pct = (self.ending_capital / self.starting_capital - 1) * 100
        total_profit_loss = self.ending_capital - self.starting_capital
        
        avg_win = np.mean([t.profit_loss_pct for t in winning_trades]) if winning_trades else 0
        avg_loss = np.mean([t.profit_loss_pct for t in losing_trades]) if losing_trades else 0
        avg_hold_days = np.mean([t.days_held for t in self.trades])
        
        # Best and worst trades
        best_trade = max(self.trades, key=lambda t: t.profit_loss_pct)
        worst_trade = min(self.trades, key=lambda t: t.profit_loss_pct)
        
        return {
            'total_trades': total_trades,
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'win_rate': win_rate,
            'loss_rate': loss_rate,
            'total_return_pct': total_return_pct,
            'total_profit_loss': total_profit_loss,
            'avg_win_pct': avg_win,
            'avg_loss_pct': avg_loss,
            'avg_hold_days': avg_hold_days,
            'best_trade': {
                'symbol': best_trade.symbol,
                'return_pct': best_trade.profit_loss_pct,
                'profit': best_trade.profit_loss
            },
            'worst_trade': {
                'symbol': worst_trade.symbol,
                'return_pct': worst_trade.profit_loss_pct,
                'loss': worst_trade.profit_loss
            }
        }
    
    def calculate_risk_metrics(self) -> Dict[str, Any]:
        """Calculate risk and volatility metrics"""
        if len(self.trades) < 2:
            return {}
        
        returns = [t.profit_loss_pct for t in self.trades]
        
        # Volatility
        volatility = np.std(returns)
        
        # Sharpe ratio (annualized)
        avg_return = np.mean(returns)
        excess_return = avg_return - (self.risk_free_rate / 252)  # Daily risk-free rate
        sharpe_ratio = excess_return / volatility if volatility > 0 else 0
        
        # Maximum drawdown
        cumulative_returns = np.cumsum([t.profit_loss for t in self.trades])
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdowns = (cumulative_returns - running_max) / self.starting_capital * 100
        max_drawdown = np.min(drawdowns) if len(drawdowns) > 0 else 0
        
        # Profit factor
        gross_profit = sum(t.profit_loss for t in self.trades if t.profit_loss > 0)
        gross_loss = abs(sum(t.profit_loss for t in self.trades if t.profit_loss < 0))
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        # Sortino ratio (downside deviation)
        negative_returns = [r for r in returns if r < 0]
        downside_deviation = np.std(negative_returns) if negative_returns else 0
        sortino_ratio = excess_return / downside_deviation if downside_deviation > 0 else 0
        
        # Calmar ratio (return / max drawdown)
        annual_return = ((self.ending_capital / self.starting_capital) ** (252 / len(self.trades)) - 1) * 100
        calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown != 0 else 0
        
        return {
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'sortino_ratio': sortino_ratio,
            'calmar_ratio': calmar_ratio,
            'max_drawdown': max_drawdown,
            'profit_factor': profit_factor,
            'gross_profit': gross_profit,
            'gross_loss': gross_loss
        }
    
    def calculate_streak_metrics(self) -> Dict[str, Any]:
        """Calculate winning/losing streak metrics"""
        if not self.trades:
            return {}
        
        consecutive_wins = consecutive_losses = 0
        max_consecutive_wins = max_consecutive_losses = 0
        current_streak_type = None
        current_streak_length = 0
        
        for trade in self.trades:
            if trade.profit_loss > 0:  # Winning trade
                if current_streak_type == 'win':
                    current_streak_length += 1
                else:
                    current_streak_type = 'win'
                    current_streak_length = 1
                max_consecutive_wins = max(max_consecutive_wins, current_streak_length)
            else:  # Losing trade
                if current_streak_type == 'loss':
                    current_streak_length += 1
                else:
                    current_streak_type = 'loss'
                    current_streak_length = 1
                max_consecutive_losses = max(max_consecutive_losses, current_streak_length)
        
        return {
            'max_consecutive_wins': max_consecutive_wins,
            'max_consecutive_losses': max_consecutive_losses,
            'current_streak_type': current_streak_type,
            'current_streak_length': current_streak_length
        }
    
    def calculate_signal_strength_analysis(self) -> Dict[str, Any]:
        """Analyze performance by signal strength"""
        signal_stats = {}
        
        for strength in [6, 7, 8]:
            strength_trades = [t for t in self.trades if t.signal_strength == strength]
            if strength_trades:
                winning_trades = [t for t in strength_trades if t.profit_loss > 0]
                signal_stats[f'{strength}/8_signals'] = {
                    'count': len(strength_trades),
                    'win_rate': len(winning_trades) / len(strength_trades) * 100,
                    'avg_return': np.mean([t.profit_loss_pct for t in strength_trades]),
                    'total_profit': sum(t.profit_loss for t in strength_trades),
                    'best_trade': max(strength_trades, key=lambda t: t.profit_loss_pct).profit_loss_pct,
                    'worst_trade': min(strength_trades, key=lambda t: t.profit_loss_pct).profit_loss_pct
                }
        
        return signal_stats
    
    def calculate_exit_reason_analysis(self) -> Dict[str, Any]:
        """Analyze performance by exit reason"""
        exit_stats = {}
        
        # Group trades by exit reason
        exit_groups = {}
        for trade in self.trades:
            reason = trade.exit_reason
            if reason not in exit_groups:
                exit_groups[reason] = []
            exit_groups[reason].append(trade)
        
        for reason, trades in exit_groups.items():
            winning_trades = [t for t in trades if t.profit_loss > 0]
            exit_stats[reason] = {
                'count': len(trades),
                'percentage': len(trades) / len(self.trades) * 100,
                'win_rate': len(winning_trades) / len(trades) * 100,
                'avg_return': np.mean([t.profit_loss_pct for t in trades]),
                'avg_hold_days': np.mean([t.days_held for t in trades]),
                'total_profit': sum(t.profit_loss for t in trades)
            }
        
        return exit_stats
    
    def calculate_monthly_performance(self) -> Dict[str, Any]:
        """Calculate monthly performance breakdown"""
        monthly_stats = {}
        
        for trade in self.trades:
            # Extract month from exit date
            exit_date = datetime.strptime(trade.exit_date, '%Y-%m-%d')
            month_key = exit_date.strftime('%Y-%m')
            
            if month_key not in monthly_stats:
                monthly_stats[month_key] = {
                    'trades': 0,
                    'profit_loss': 0,
                    'winning_trades': 0,
                    'returns': []
                }
            
            monthly_stats[month_key]['trades'] += 1
            monthly_stats[month_key]['profit_loss'] += trade.profit_loss
            monthly_stats[month_key]['returns'].append(trade.profit_loss_pct)
            
            if trade.profit_loss > 0:
                monthly_stats[month_key]['winning_trades'] += 1
        
        # Calculate additional metrics for each month
        for month, stats in monthly_stats.items():
            stats['win_rate'] = stats['winning_trades'] / stats['trades'] * 100
            stats['avg_return'] = np.mean(stats['returns'])
            stats['volatility'] = np.std(stats['returns'])
            del stats['returns']  # Remove raw returns to save space
        
        return monthly_stats
    
    def calculate_trading_costs_analysis(self) -> Dict[str, Any]:
        """Analyze impact of trading costs"""
        if not any(hasattr(t, 'trading_costs') and t.trading_costs > 0 for t in self.trades):
            return {}
        
        total_costs = sum(getattr(t, 'trading_costs', 0) for t in self.trades)
        avg_cost_per_trade = total_costs / len(self.trades)
        
        # Calculate performance without costs
        gross_profit = sum(t.profit_loss + getattr(t, 'trading_costs', 0) for t in self.trades)
        gross_return_pct = gross_profit / self.starting_capital * 100
        
        cost_impact_pct = total_costs / self.starting_capital * 100
        
        return {
            'total_trading_costs': total_costs,
            'avg_cost_per_trade': avg_cost_per_trade,
            'cost_impact_pct': cost_impact_pct,
            'gross_return_pct': gross_return_pct,
            'net_return_pct': (self.ending_capital / self.starting_capital - 1) * 100,
            'cost_as_pct_of_gross_profit': total_costs / gross_profit * 100 if gross_profit > 0 else 0
        }
    
    def generate_comprehensive_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        report = {
            'basic_metrics': self.calculate_basic_metrics(),
            'risk_metrics': self.calculate_risk_metrics(),
            'streak_metrics': self.calculate_streak_metrics(),
            'signal_strength_analysis': self.calculate_signal_strength_analysis(),
            'exit_reason_analysis': self.calculate_exit_reason_analysis(),
            'monthly_performance': self.calculate_monthly_performance(),
            'trading_costs_analysis': self.calculate_trading_costs_analysis(),
            'capital_summary': {
                'starting_capital': self.starting_capital,
                'ending_capital': self.ending_capital,
                'total_return': self.ending_capital - self.starting_capital,
                'return_percentage': (self.ending_capital / self.starting_capital - 1) * 100
            }
        }
        
        return report
    
    def print_performance_summary(self):
        """Print formatted performance summary"""
        metrics = self.calculate_basic_metrics()
        risk_metrics = self.calculate_risk_metrics()
        
        if not metrics:
            print("❌ No trades to analyze!")
            return
        
        print("\n" + "=" * 70)
        print("📊 PERFORMANCE ANALYSIS SUMMARY")
        print("=" * 70)
        
        print(f"💰 Starting Capital: ${self.starting_capital:,}")
        print(f"💰 Ending Capital: ${self.ending_capital:,}")
        print(f"📈 Total Return: {metrics['total_return_pct']:+.2f}%")
        print(f"💵 Net Profit/Loss: ${metrics['total_profit_loss']:+,.0f}")
        
        print(f"\n📊 TRADE STATISTICS:")
        print(f"🔢 Total Trades: {metrics['total_trades']}")
        print(f"✅ Win Rate: {metrics['win_rate']:.1f}%")
        print(f"❌ Loss Rate: {metrics['loss_rate']:.1f}%")
        print(f"📊 Average Win: {metrics['avg_win_pct']:+.2f}%")
        print(f"📊 Average Loss: {metrics['avg_loss_pct']:+.2f}%")
        print(f"⏱️  Average Hold: {metrics['avg_hold_days']:.1f} days")
        
        if risk_metrics:
            print(f"\n📊 RISK METRICS:")
            print(f"📉 Max Drawdown: {risk_metrics['max_drawdown']:.2f}%")
            print(f"📊 Volatility: {risk_metrics['volatility']:.2f}%")
            print(f"⚡ Sharpe Ratio: {risk_metrics['sharpe_ratio']:.2f}")
            print(f"💪 Profit Factor: {risk_metrics['profit_factor']:.2f}")
        
        print(f"\n🏆 BEST/WORST:")
        print(f"🥇 Best Trade: {metrics['best_trade']['symbol']} ({metrics['best_trade']['return_pct']:+.2f}%)")
        print(f"🥉 Worst Trade: {metrics['worst_trade']['symbol']} ({metrics['worst_trade']['return_pct']:+.2f}%)")

# Example usage
if __name__ == "__main__":
    # Create sample trades for testing
    sample_trades = [
        Trade("AAPL", "2024-01-01", 150.0, "2024-01-05", 155.0, 100, 7, 500.0, 3.33, 4, "8EMA break", 10.0),
        Trade("MSFT", "2024-01-02", 200.0, "2024-01-08", 190.0, 50, 6, -500.0, -5.0, 6, "8EMA break", 8.0),
        Trade("GOOGL", "2024-01-03", 100.0, "2024-01-10", 110.0, 200, 8, 2000.0, 10.0, 7, "Large profit protection", 15.0),
    ]
    
    analyzer = PerformanceAnalyzer(sample_trades, 30000, 32000)
    
    # Print summary
    analyzer.print_performance_summary()
    
    # Generate full report
    report = analyzer.generate_comprehensive_report()
    
    # Save report to JSON
    with open('performance_report.json', 'w') as f:
        json.dump(report, f, indent=2, default=str)
    
    print(f"\n💾 Full report saved to: performance_report.json")
