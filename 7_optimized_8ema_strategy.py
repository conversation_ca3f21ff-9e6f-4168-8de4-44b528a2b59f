#!/usr/bin/env python3
"""
7. Optimized 8EMA Strategy - Enhanced momentum strategy with compound reinvestment
Focus on 8EMA exits with optimized entry signals for better performance
"""

import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional

from 1_base_backtester import BaseBacktester, Trade
from 3_trading_costs_calculator import SimplifiedCosts
from 4_risk_management import RiskManager
from 5_performance_metrics import PerformanceAnalyzer

class Optimized8EMAStrategy(BaseBacktester):
    """
    Optimized 8EMA Strategy Implementation
    - Enhanced entry signals with multiple confirmations
    - Primary 8EMA exits with profit protection
    - Compound reinvestment for growth
    - Reduced whipsaws and improved quality
    """
    
    def __init__(self, starting_capital: float = 30000):
        super().__init__(starting_capital)
        
        # Optimized Parameters
        self.max_position_pct = 0.08  # 8% max per position
        self.min_signal_strength = 6  # Minimum 6/8 signals for entry
        self.min_trade_gap = 2  # Days between trades on same symbol (reduce whipsaws)
        
        # Initialize components
        self.risk_manager = RiskManager(starting_capital)
        self.cost_calculator = SimplifiedCosts()
        
        # Enhanced Stock Universe (focus on liquid, trending stocks)
        self.stocks = [
            # High Beta Tech (good for momentum)
            'NVDA', 'AMD', 'TSLA', 'META', 'GOOGL', 'AAPL', 'MSFT', 'AMZN',
            # Growth Stocks
            'AVGO', 'CRM', 'ORCL', 'ADBE', 'NFLX', 'INTU', 'NOW',
            # Momentum Favorites  
            'LRCX', 'AMAT', 'KLAC', 'ADI', 'TXN', 'QCOM',
            # Financial Momentum
            'JPM', 'BAC', 'GS', 'AXP', 'SCHW',
            # Industrial/Energy
            'CAT', 'BA', 'RTX', 'XOM', 'CVX', 'COP',
            # Healthcare Growth
            'UNH', 'JNJ', 'PFE', 'ABBV', 'TMO', 'DHR',
            # Consumer Discretionary
            'HD', 'LOW', 'NKE', 'DIS', 'MCD',
            # Communication
            'VZ', 'T', 'TMUS', 'CMCSA'
        ]
        
        # Track recent trades to avoid whipsaws
        self.recent_trades = {}

    def calculate_indicators(self, df):
        """Calculate enhanced technical indicators"""
        # EMAs (key for exits)
        df['ema8'] = df['close'].ewm(span=8).mean()
        df['ema21'] = df['close'].ewm(span=21).mean()
        df['ema50'] = df['close'].ewm(span=50).mean()
        
        # Bollinger Bands
        df['bb_middle'] = df['close'].rolling(20).mean()
        df['bb_std'] = df['close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (df['bb_std'] * 2)
        df['bb_lower'] = df['bb_middle'] - (df['bb_std'] * 2)
        
        # Keltner Channels
        df['high_low'] = df['high'] - df['low']
        df['high_close'] = abs(df['high'] - df['close'].shift(1))
        df['low_close'] = abs(df['low'] - df['close'].shift(1))
        df['true_range'] = df[['high_low', 'high_close', 'low_close']].max(axis=1)
        df['atr14'] = df['true_range'].rolling(14).mean()
        
        df['kc_middle'] = df['ema21']
        df['kc_upper'] = df['kc_middle'] + (df['atr14'] * 1.5)
        df['kc_lower'] = df['kc_middle'] - (df['atr14'] * 1.5)
        
        # TTM Squeeze
        df['squeeze'] = (df['bb_upper'] < df['kc_upper']) & (df['bb_lower'] > df['kc_lower'])
        
        # Momentum
        df['sma20'] = df['close'].rolling(20).mean()
        df['momentum'] = df['close'] - df['sma20']
        df['histogram'] = df['momentum'] - df['momentum'].shift(1)
        
        # Trend strength
        df['trend_strength'] = (df['ema8'] > df['ema21']).astype(int) + \
                              (df['ema21'] > df['ema50']).astype(int) + \
                              (df['close'] > df['ema8']).astype(int)
        
        # Volume (if available)
        if 'volume' in df.columns:
            df['volume_avg20'] = df['volume'].rolling(20).mean()
        
        return df

    def check_enhanced_entry_signal(self, df, idx):
        """Enhanced entry signal with better filtering"""
        if idx < 10:
            return False, 0
            
        current = df.iloc[idx]
        prev = df.iloc[idx-1]
        prev2 = df.iloc[idx-2]
        
        score = 0
        
        try:
            # 1. Strong trend alignment (EMA8 > EMA21 > EMA50)
            if current['trend_strength'] >= 2:
                score += 2
            
            # 2. Squeeze firing or momentum building
            if (prev['squeeze'] and not current['squeeze']) or current['histogram'] > prev['histogram']:
                score += 1
            
            # 3. Price above 8EMA with momentum
            if current['close'] > current['ema8'] and current['momentum'] > 0:
                score += 2
            
            # 4. Volume confirmation (if available)
            if 'volume' in current and 'volume_avg20' in current:
                if pd.notna(current['volume_avg20']) and current['volume'] > current['volume_avg20']:
                    score += 1
            else:
                score += 1  # Give benefit of doubt if no volume data
            
            # 5. Breakout above recent resistance
            recent_high = df.iloc[max(0, idx-5):idx]['high'].max()
            if current['close'] > recent_high * 1.005:  # 0.5% breakout
                score += 1
            
            # 6. ATR filter (avoid low volatility periods)
            if current['atr14'] > df['atr14'].rolling(20).mean().iloc[idx] * 0.8:
                score += 1
                
        except Exception as e:
            print(f"Error in entry signal calculation: {e}")
            return False, 0
        
        return score >= self.min_signal_strength, score

    def calculate_position_size(self, signal_strength: int, current_capital: float, price: float) -> int:
        """Optimized position sizing with compound reinvestment"""
        
        # Base allocation by signal strength (more aggressive for stronger signals)
        allocation_pct = {
            6: 0.04,   # 4% for 6/8 signals
            7: 0.06,   # 6% for 7/8 signals  
            8: 0.08    # 8% for 8/8 signals (max allocation)
        }
        
        base_allocation = allocation_pct.get(signal_strength, 0.03)
        
        # Calculate position value using current capital (compound effect)
        position_value = current_capital * base_allocation
        shares = int(position_value / price)
        
        return max(shares, 1) if shares > 0 else 0

    def check_8ema_exit(self, df, entry_idx, current_idx):
        """Optimized 8EMA exit with additional filters"""
        if current_idx <= entry_idx:
            return False, "No exit"
        
        current = df.iloc[current_idx]
        entry = df.iloc[entry_idx]
        
        # Primary Exit: 8EMA break
        if current['close'] < current['ema8']:
            # Additional confirmation: ensure it's not just a brief dip
            if current_idx > entry_idx + 1:
                prev = df.iloc[current_idx-1]
                if prev['close'] < prev['ema8']:  # Two consecutive closes below 8EMA
                    return True, "8EMA confirmed break"
            return True, "8EMA break"
        
        # Profit protection: Take profits on large moves
        profit_pct = (current['close'] / entry['close'] - 1) * 100
        if profit_pct > 15:  # 15% profit
            return True, "Large profit protection"
        
        # Maximum hold period (prevent dead money)
        days_held = current_idx - entry_idx
        if days_held >= 15:
            return True, "Max hold period"
        
        return False, "Hold"

    async def backtest_symbol(self, symbol, start_date, end_date):
        """Backtest single symbol with enhanced logic"""
        df = await self.fetch_historical_data(symbol, start_date, end_date)
        if df is None or len(df) < 60:
            return
        
        df = self.calculate_indicators(df)
        
        i = 25  # Start after indicators are calculated
        open_position = None
        
        while i < len(df):
            current_date = df.iloc[i]['date']
            
            # Check for exit if we have an open position
            if open_position:
                should_exit, exit_reason = self.check_8ema_exit(df, open_position['entry_idx'], i)
                
                if should_exit:
                    # Close position with realistic execution
                    exit_price = df.iloc[i]['close']
                    days_held = i - open_position['entry_idx']
                    
                    # Calculate trading costs
                    exit_costs = self.cost_calculator.calculate_exit_costs(
                        open_position['shares'], exit_price
                    )
                    total_costs = open_position.get('entry_costs', 0) + exit_costs
                    
                    # Net P&L after all costs
                    gross_pnl = (exit_price - open_position['entry_price']) * open_position['shares']
                    net_pnl = gross_pnl - total_costs
                    profit_loss_pct = (exit_price / open_position['entry_price'] - 1) * 100
                    
                    trade = Trade(
                        symbol=symbol,
                        entry_date=open_position['entry_date'],
                        entry_price=open_position['entry_price'],
                        exit_date=current_date.strftime('%Y-%m-%d'),
                        exit_price=exit_price,
                        position_size=open_position['shares'],
                        signal_strength=open_position['signal_strength'],
                        profit_loss=net_pnl,
                        profit_loss_pct=profit_loss_pct,
                        days_held=days_held,
                        exit_reason=exit_reason,
                        trading_costs=total_costs
                    )
                    
                    self.trades.append(trade)
                    
                    # COMPOUND REINVESTMENT: Add/subtract P&L to capital
                    self.current_capital += net_pnl
                    
                    # Track recent trade to avoid immediate re-entry
                    self.recent_trades[symbol] = i
                    
                    print(f"TRADE: {symbol} {profit_loss_pct:+.1f}% ({exit_reason}) - Capital: ${self.current_capital:,.0f}")
                    
                    open_position = None
            
            # Check for new entry signal
            if not open_position and self.current_capital > 1000:
                # Avoid whipsaw trades
                if symbol in self.recent_trades:
                    if i - self.recent_trades[symbol] < self.min_trade_gap:
                        i += 1
                        continue
                
                has_signal, signal_strength = self.check_enhanced_entry_signal(df, i)
                
                if has_signal:
                    entry_price = df.iloc[i]['close']
                    shares = self.calculate_position_size(signal_strength, self.current_capital, entry_price)
                    
                    if shares > 0:
                        # Calculate entry costs
                        entry_costs = self.cost_calculator.calculate_entry_costs(shares, entry_price)
                        position_cost = shares * entry_price + entry_costs
                        
                        if position_cost <= self.current_capital * 0.95:  # Keep 5% cash buffer
                            # Deduct entry costs
                            self.current_capital -= entry_costs
                            
                            open_position = {
                                'entry_idx': i,
                                'entry_date': current_date.strftime('%Y-%m-%d'),
                                'entry_price': entry_price,
                                'shares': shares,
                                'signal_strength': signal_strength,
                                'entry_costs': entry_costs
                            }
                            
                            # Record signal
                            self.signals.append({
                                'symbol': symbol,
                                'date': current_date.strftime('%Y-%m-%d'),
                                'price': entry_price,
                                'signal_strength': signal_strength,
                                'capital_at_entry': self.current_capital
                            })
            
            i += 1

    async def run_backtest(self, months: int = 6):
        """Run optimized 8EMA backtest"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=months * 30)
        
        start_str = start_date.strftime('%Y-%m-%d')
        end_str = end_date.strftime('%Y-%m-%d')
        
        print(f"🚀 OPTIMIZED 8EMA STRATEGY WITH COMPOUND REINVESTMENT")
        print(f"📅 Period: {start_str} to {end_str}")
        print(f"💰 Starting Capital: ${self.starting_capital:,}")
        print(f"📊 Testing {len(self.stocks)} optimized stocks")
        print(f"🎯 Primary Exit: 8EMA break")
        print(f"💹 Auto-Reinvestment: Enabled")
        print(f"💸 Trading Costs: Included")
        print("=" * 70)
        
        # Process stocks
        batch_size = 10
        for i in range(0, len(self.stocks), batch_size):
            batch = self.stocks[i:i + batch_size]
            print(f"Processing batch {i//batch_size + 1}: {batch[0]} to {batch[-1]}")
            
            tasks = [self.backtest_symbol(symbol, start_str, end_str) for symbol in batch]
            await asyncio.gather(*tasks, return_exceptions=True)
            
            await asyncio.sleep(1.0)  # Rate limiting
        
        return self.generate_results()

    def generate_results(self):
        """Generate comprehensive results"""
        if not self.trades:
            print("❌ No trades executed!")
            return {}
        
        # Use performance analyzer
        analyzer = PerformanceAnalyzer(self.trades, self.starting_capital, self.current_capital)
        
        # Print performance summary
        analyzer.print_performance_summary()
        
        # Generate comprehensive report
        report = analyzer.generate_comprehensive_report()
        
        # Compound growth analysis
        print(f"\n💹 COMPOUND GROWTH ANALYSIS:")
        print(f"📊 Capital Growth: ${self.starting_capital:,} → ${self.current_capital:,}")
        total_return_pct = (self.current_capital / self.starting_capital - 1) * 100
        print(f"📈 Compound Return: {total_return_pct:+.2f}% over {len(self.trades)} trades")
        
        # Save results
        results = {
            'strategy': 'Optimized_8EMA',
            'parameters': {
                'min_signal_strength': self.min_signal_strength,
                'max_position_pct': self.max_position_pct,
                'starting_capital': self.starting_capital,
                'ending_capital': self.current_capital
            },
            'performance_report': report,
            'trades': [trade.__dict__ for trade in self.trades],
            'signals': self.signals
        }
        
        import json
        with open('optimized_8ema_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Results saved to: optimized_8ema_results.json")
        return results

async def main():
    """Run Optimized 8EMA strategy backtest"""
    async with Optimized8EMAStrategy(starting_capital=30000) as strategy:
        results = await strategy.run_backtest(months=6)
        return results

if __name__ == "__main__":
    import pandas as pd
    asyncio.run(main())
